consul:
  host: ***********
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.ubear.co/
  upload:
    url: http://**************:9333
instance-zone: 10087
jiuji:
  sys:
    moa: https://moa.ubear.co
    pc: https://oa.ubear.co
    xtenant: 10087
  xtenant: 87000
logging:
  config:
    path: classpath:log/log4j2-dev.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10087:uLuCfE1AvouE@***********:27017/ch999oa__10087
  url1: **********************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10087
    password: 0sugHFYM0mAh
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10087
  oa_nc:
    dbname: oa_nc__10087
    password: UC6d#i8oUVRA
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10087
office:
  sys:
    xtenant: 10087
rabbitmq:
  master:
    password: evTiE
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10087
    vhost: oaAsync__10087
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: wxwYd
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10087
    vhost: oa__10087
  oaAsync:
    password: evTiE
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10087
    vhost: oaAsync__10087
  printer:
    password: nhxGI
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10087
    vhost: printer__10087
redis:
  oa:
    host: ***********
    password: google99
    port: 6387
    url: google99@***********:6387
sms:
  send:
    email:
      url: http://sms.ubear.co/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.ubear.co/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10087
sqlserver:
  after_write:
    dbname: ch999oanew__10087
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "P6oKYu3N7Jve"
    port: 1433
    username: ch999oanew__10087
  ch999oanew:
    dbname: ch999oanew__10087
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "P6oKYu3N7Jve"
    port: 1433
    username: ch999oanew__10087
  ch999oanewReport:
    dbname: ch999oanew__10087
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "P6oKYu3N7Jve"
    port: 1433
    username: ch999oanew__10087
  ch999oanewHis:
    dbname: ch999oanew__10087
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "P6oKYu3N7Jve"
    port: 1433
    username: ch999oanew__10087
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10087
    host: sqlserver.serv.hb.saas.ch999.cn
    password: peIeKzWKigZM
    port: 1433
    username: office__10087
  oanewWrite:
    dbname: ch999oanew__10087
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "P6oKYu3N7Jve"
    port: 1433
    username: ch999oanew__10087
  office:
    dbname: office__10087
    host: sqlserver.serv.hb.saas.ch999.cn
    password: peIeKzWKigZM
    port: 1433
    username: office__10087
  officeWrite:
    dbname: office__10087
    host: sqlserver.serv.hb.saas.ch999.cn
    password: peIeKzWKigZM
    port: 1433
    username: office__10087
  smallpro_write:
    dbname: ch999oanew__10087
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "P6oKYu3N7Jve"
    port: 1433
    username: ch999oanew__10087
  web999:
    dbname: web999__10087
    host: sqlserver.serv.hb.saas.ch999.cn
    password: VQ4ABV7ar3rX
    port: 1433
    username: web999__10087
url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.ubear.co/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.ubear.co/
  source:
      path: i18n/url
  uploadImgUrl: http://data3:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'