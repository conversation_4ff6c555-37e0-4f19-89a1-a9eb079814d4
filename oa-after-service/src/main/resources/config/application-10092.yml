consul:
  host: ***********
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.0576sj.com/
  upload:
    url: http://**************:9333
instance-zone: 10092
jiuji:
  sys:
    moa: https://moa.0576sj.com
    pc: https://oa.0576sj.com
    xtenant: 10092
  xtenant: 92000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10092:kXtCorbnFgpc@***********:27017,***********:27017,***********:27017/ch999oa__10092
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname:
    password:
    url: :3306
    username:
  oa_core:
    dbname: oa_core__10092
    password: dQ9U8v1pFk2i
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10092
  oa_nc:
    dbname: oa_nc__10092
    password: 4ZjUaFOTZaFk
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10092
office:
  sys:
    xtenant: 10092
rabbitmq:
  master:
    password: znQUH
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10092
    vhost: oaAsync__10092
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: TcIvt
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10092
    vhost: oa__10092
  oaAsync:
    password: znQUH
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10092
    vhost: oaAsync__10092
  printer:
    password: EGJAD
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10092
    vhost: printer__10092
redis:
  oa:
    host: ***********
    password: google99
    port: 6380
    url: google99@***********:6380
sms:
  send:
    email:
      url: http://sms.0576sj.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.0576sj.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10092
sqlserver:
  after_write:
    dbname: ch999oanew__10092
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "EWo2Sea1iauh"
    port: 1433
    username: ch999oanew__10092
  ch999oanew:
    dbname: ch999oanew__10092
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "EWo2Sea1iauh"
    port: 1433
    username: ch999oanew__10092
  ch999oanewReport:
    dbname: ch999oanew__10092
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "EWo2Sea1iauh"
    port: 1433
    username: ch999oanew__10092
  ch999oanewHis:
    dbname: ch999oanew__10092
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "EWo2Sea1iauh"
    port: 1433
    username: ch999oanew__10092
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10092
    host: sqlserver.serv.hd.saas.ch999.cn
    password: EuF5ZszjE94C
    port: 1433
    username: office__10092
  oanewWrite:
    dbname: ch999oanew__10092
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "EWo2Sea1iauh"
    port: 1433
    username: ch999oanew__10092
  office:
    dbname: office__10092
    host: sqlserver.serv.hd.saas.ch999.cn
    password: EuF5ZszjE94C
    port: 1433
    username: office__10092
  officeWrite:
    dbname: office__10092
    host: sqlserver.serv.hd.saas.ch999.cn
    password: EuF5ZszjE94C
    port: 1433
    username: office__10092
  smallpro_write:
    dbname: ch999oanew__10092
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "EWo2Sea1iauh"
    port: 1433
    username: ch999oanew__10092
  web999:
    dbname: web999__10092
    host: sqlserver.serv.hd.saas.ch999.cn
    password: cQX3NGpDIY1i
    port: 1433
    username: web999__10092
url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.0576sj.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.0576sj.com/
  source:
      path: i18n/url
  uploadImgUrl: http://data3:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: afterservice-default-topic
  clientId: oa-afterservice-${random.value}
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
