consul:
  host: ***********
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://nycl.img.saas.ch999.cn/
  upload:
    url: http://**************:9333
instance-zone: 10093
jiuji:
  sys:
    moa: https://nycl.moa.saas.ch999.cn
    pc: https://nycl.oa.saas.ch999.cn
    xtenant: 10093
  xtenant: 93000
logging:
  config:
    path: classpath:log/log4j2-dev.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10093:IWNu4Vd46hm2@***********:27017,***********:27017,***********:27017/ch999oa__10093
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10093
    password: gP#ojjBj^Y4e
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10093
  oa_nc:
    dbname: oa_nc__10093
    password: CDI0tOVOSPOD
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10093
office:
  sys:
    xtenant: 10093
rabbitmq:
  master:
    password: zbfum
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10093
    vhost: oaAsync__10093
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: hOSsa
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10093
    vhost: oa__10093
  oaAsync:
    password: zbfum
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10093
    vhost: oaAsync__10093
  printer:
    password: txFCK
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10093
    vhost: printer__10093
redis:
  oa:
    host: ***********
    password: google99
    port: 6383
    url: google99@***********:6383
sms:
  send:
    email:
      url: http://nycl.sms.saas.ch999.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://nycl.sms.saas.ch999.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10093
sqlserver:
  after_write:
    dbname: ch999oanew__10093
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "KJeSAR47Xp6i"
    port: 1433
    username: ch999oanew__10093
  ch999oanew:
    dbname: ch999oanew__10093
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "KJeSAR47Xp6i"
    port: 1433
    username: ch999oanew__10093
  ch999oanewReport:
    dbname: ch999oanew__10093
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "KJeSAR47Xp6i"
    port: 1433
    username: ch999oanew__10093
  ch999oanewHis:
    dbname: ch999oanew__10093
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "KJeSAR47Xp6i"
    port: 1433
    username: ch999oanew__10093
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10093
    host: sqlserver.serv.hd.saas.ch999.cn
    password: 0CstUw89qyzc
    port: 1433
    username: office__10093
  oanewWrite:
    dbname: ch999oanew__10093
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "KJeSAR47Xp6i"
    port: 1433
    username: ch999oanew__10093
  office:
    dbname: office__10093
    host: sqlserver.serv.hd.saas.ch999.cn
    password: 0CstUw89qyzc
    port: 1433
    username: office__10093
  officeWrite:
    dbname: office__10093
    host: sqlserver.serv.hd.saas.ch999.cn
    password: 0CstUw89qyzc
    port: 1433
    username: office__10093
  smallpro_write:
    dbname: ch999oanew__10093
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "KJeSAR47Xp6i"
    port: 1433
    username: ch999oanew__10093
  web999:
    dbname: web999__10093
    host: sqlserver.serv.hd.saas.ch999.cn
    password: ZP4wKnyGSwbU
    port: 1433
    username: web999__10093
url:
  delImgUrl: http://data3:5083
  oa-push-info: http://nycl.inwcf.saas.ch999.cn/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://nycl.img.saas.ch999.cn/
  source:
      path: i18n/url
  uploadImgUrl: http://data3:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'