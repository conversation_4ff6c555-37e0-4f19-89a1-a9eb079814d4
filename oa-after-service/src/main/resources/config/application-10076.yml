consul:
  host: ***********
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.life99.cn/
  upload:
    url: http://**************:9333
instance-zone: 10076
jiuji:
  sys:
    moa: https://moa.life99.cn
    pc: https://oa.life99.cn
    xtenant: 10076
  xtenant: 76000
logging:
  config:
    path: classpath:log/log4j2-dev.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10076:u3K3pgjctmIt@***********:27017,***********:27017,***********:27017/ch999oa__10076
  url1: **********************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10076
    password: f1i5XbEFwOm9
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10076
  oa_nc:
    dbname: oa_nc__10076
    password: OYf3Vq0k3Bkt
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10076
office:
  sys:
    xtenant: 10076
rabbitmq:
  master:
    password: BtSmO
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10076
    vhost: oaAsync__10076
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: jDQJn
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10076
    vhost: oa__10076
  oaAsync:
    password: BtSmO
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10076
    vhost: oaAsync__10076
  printer:
    password: MaPQr
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10076
    vhost: printer__10076
redis:
  oa:
    host: ***********
    password: google99
    port: 6381
    url: google99@***********:6381
sms:
  send:
    email:
      url: http://sms.life99.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.life99.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10076
sqlserver:
  after_write:
    dbname: ch999oanew__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "Tp85as7v3yjT"
    port: 1433
    username: ch999oanew__10076
  ch999oanew:
    dbname: ch999oanew__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "Tp85as7v3yjT"
    port: 1433
    username: ch999oanew__10076
  ch999oanewReport:
    dbname: ch999oanew__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "Tp85as7v3yjT"
    port: 1433
    username: ch999oanew__10076
  ch999oanewHis:
    dbname: ch999oanew__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "Tp85as7v3yjT"
    port: 1433
    username: ch999oanew__10076
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: pKphX5A20Opi
    port: 1433
    username: office__10076
  oanewWrite:
    dbname: ch999oanew__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "Tp85as7v3yjT"
    port: 1433
    username: ch999oanew__10076
  office:
    dbname: office__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: pKphX5A20Opi
    port: 1433
    username: office__10076
  officeWrite:
    dbname: office__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: pKphX5A20Opi
    port: 1433
    username: office__10076
  smallpro_write:
    dbname: ch999oanew__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "Tp85as7v3yjT"
    port: 1433
    username: ch999oanew__10076
  web999:
    dbname: web999__10076
    host: sqlserver.serv.hb.saas.ch999.cn
    password: l5s294FsYkeO
    port: 1433
    username: web999__10076
url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.life99.cn/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.life99.cn/
  source:
      path: i18n/url
  uploadImgUrl: http://data3:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'


apollo:
  url: {{.ApolloConfig.Meta}}
  file: application-after.yml

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: afterservice-default-topic
  clientId: oa-afterservice-${random.value}
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
