<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ServiceRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ServiceRecord">
        <id column="id" property="id" />
        <result column="basket_id" property="basketId" />
        <result column="sub_id" property="subId" />
        <result column="basket_idBind" property="basketIdbind" />
        <result column="area" property="area" />
        <result column="tradedate" property="tradedate" />
        <result column="imei" property="imei" />
        <result column="ServiceType" property="serviceType" />
        <result column="userid" property="userid" />
        <result column="isdel" property="isdel" />
        <result column="price" property="price" />
        <result column="feiyong" property="feiyong" />
        <result column="areaid" property="areaid" />
        <result column="servicesTypeBindId" property="servicesTypeBindId" />
    </resultMap>
    <select id="getTop1UseServiceRecord" resultType="com.jiuji.oa.afterservice.bigpro.bo.servicerecord.UseServiceRecordBo">
        SELECT top 1 id,isnull(server_shouhou_id,0) serverShouhouId,isnull(feiyong,0) feiYong
        FROM ServiceRecord with(nolock) WHERE isnull(isdel,0) = 0
        and imei in
            <foreach collection="outServiceImeis" item="outServiceImei" separator="," open="(" close=")">
                #{outServiceImei}
            </foreach>
        and (
                isnull(#{shouhou.serversOutDtime},#{shouhou.modidate}) &gt;= startTime
                <!--endTime 为当天的最后一刻, 出险时间设置为当天开始来进行比较 -->
                and CONVERT(DATETIME, CONVERT(NVARCHAR(10),isnull(#{shouhou.serversOutDtime},#{shouhou.modidate}), 120)) &lt;= endTime
                <if test="isGaoJiOutService != null and isGaoJiOutService">
                    or #{shouhou.modidate} &gt; endTime
                </if>
            )
        and isnull(classification,0) in(0,1)
        and ServiceType in
        <foreach collection="serviceTypes" separator="," open="(" close=")" item="serviceType">
            #{serviceType}
        </foreach>
        ORDER by serverShouhouId desc,feiYong desc,id asc
    </select>
    <update id="useServiceRecord">
        update ServiceRecord set feiyong=price,server_shouhou_id=#{shouhou.id}
        where isnull(isdel,0) = 0 and server_shouhou_id is null and isnull(feiyong,0) = 0 and id = #{serviceRecordId}
    </update>
    <select id="getSub" resultType="com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO">
        select top 1 s.sub_to as subTo,b.ppriceid as ppriceId,k.id as mkcId,s.tradedate as tradeDate,k.imei as imei
             ,s.userid as userId,s.areaid as mkcAreaId,s.sub_mobile subMobile,b.sub_id subId,b.basket_id as basketId
             ,isnull(b.type,0) as basketType,s.subtype subType,b.price as price,s.tradeDate1 as transactionDate
        from product_mkc k with(nolock)
             left join basket b with(nolock) on k.basket_id=b.basket_id
             left join sub s with(nolock) on b.sub_id=s.sub_id
        where k.imei in
        <foreach collection="imeis" open="(" close=")" item="imei" separator=",">
            #{imei}
        </foreach>
        and s.sub_check not in(4,8,9)
        order by s.tradeDate1 desc
    </select>
    <select id="getCareId" resultType="java.lang.Integer">
        SELECT top 1 r.id FROM dbo.ServiceRecord r WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON r.basket_id=b.basket_id
        INNER JOIN dbo.sub s WITH(NOLOCK) ON s.sub_id=b.sub_id
        <where>
            and r.ServiceType = 21
            and r.imei in
            <foreach collection="imeis" open="(" close=")" item="imei" separator=",">
                #{imei}
            </foreach>
            AND b.ppriceid=85422 AND s.sub_check not in(4,8,9)
            and s.userid = #{userId}
        </where>
        order by r.id desc
    </select>
    <select id="getHistoryRecord" resultType="com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO">
        seLect top 1 r.userid as userId,p.product_name as productName,u.mobile as subMobile,r.tradedate as tradeDate
             ,r.areaid as mkcAreaId,u.userclass as userClass,u.UserName as username,u.blacklist as blacklist,r.product_color as productColor
             ,r.product_id as productId,r.ppriceid as ppriceId,r.sub_id as subId,r.basket_id as basketId,r.imei as imei
             ,p.brandid as brandId,p.cid as cid
        from record1 r with(nolock)
             left join BBSXP_Users u with(nolock) on r.userid=u.ID
             left join productinfo p with(nolock) on p.ppriceid=r.ppriceid
        <where>
            and r.imei=#{imei}
            <if test="userId != null and userId != 0">
               and r.userid=#{userId}
            </if>
        </where>
        order by r.id desc
    </select>
    <select id="list9jiServiceRecord" resultType="com.jiuji.oa.afterservice.bigpro.po.ServiceRecord">
        select  sr.id,sr.areaid,sr.tradedate,sr.imei,sr.basket_idBind,sr.ServiceType,isnull(sr.price,0) as price
             ,isnull(sr.feiyong,0) as feiyong,sr.startTime,sr.endTime,sr.ppriceid,sr.servicesTypeBindId
        from ServiceRecord sr with(nolock)
        where isnull(sr.isdel,0)=0
          <if test="serviceTypes != null">
              <foreach collection="serviceTypes" item="serviceType" open="and sr.ServiceType in (" close=")" separator=",">
                  #{serviceType}
              </foreach>
          </if>
          <if test="serversOutDtime != null">
                and  #{serversOutDtime} between sr.startTime and sr.endTime
          </if>
          and sr.imei=#{imei}  ORDER BY sr.tradedate DESC
    </select>
    <select id="listShouhouService" resultType="com.jiuji.oa.afterservice.bigpro.po.ServiceRecord">
        SELECT id,areaid,tradedate,imei,basket_idBind,ServiceType,ISNULL(price,0) AS price,ISNULL(feiyong,0) AS feiyong,
               ppriceid,servicesTypeBindId,startTime,endTime,server_shouhou_id serverShouhouId,classification
        FROM (
                 SELECT id,areaid
                      , tradedate
                      , imei
                      , basket_idBind
                      , ServiceType
                      , ISNULL(price, 0)   AS price
                      , ISNULL(feiyong, 0) AS feiyong
                      , ppriceid,servicesTypeBindId,classification
                      , startTime
                      , endTime
                      , server_shouhou_id
                      , ROW_NUMBER()          OVER(PARTITION BY ServiceType ORDER BY tradedate DESC) r
                 FROM ServiceRecord WITH(NOLOCK)
                 WHERE isnull(isdel,0)=0 AND ServiceType IN
                    <foreach collection="shouhouServiceTypes" item="shouhouServiceType" open="(" close=")" separator="," >
                        #{shouhouServiceType}
                    </foreach>
                   AND imei=#{imei}
                    <if test="transactionDate != null">
                        and tradedate &gt;= #{transactionDate}
                    </if>
             ) t
        WHERE t.r=1 ORDER BY tradedate DESC
    </select>
    <select id="getLpSub" resultType="com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO">
        select top 1 s.sub_to subTo,b.ppriceid as ppriceId, k.id as mkcId,isnull(s.tradedate,GETDATE()) as tradeDate,k.imei as imei,s.userid as userId
                   ,s.areaid as mkcAreaId,s.sub_mobile as subMobile,b.sub_id as subId,b.basket_id as basketId
                    ,b.type as basketType,s.saleType as subType,b.price as price,s.tradeDate1 as transactionDate
        from   dbo.recover_mkc k with(nolock)
            left join dbo.recover_marketSubInfo b with(nolock) on k.to_basket_id = b.basket_id
            left join dbo.recover_marketInfo s with(nolock) on b.sub_id = s.sub_id
        <where>
             k.imei =#{imei} and s.sub_check in ( 1,2,6,3) AND ISNULL(s.saleType,0)=0
             <if test="userId != null and userId != 0">
                 and s.userid=#{userId}
             </if>
        </where>
        order by isnull(s.tradeDate1,s.tradeDate) desc
    </select>
    <select id="getStopShouhouInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.servicerecord.StopShouhouInfoBo">
        select top 1 sh.id,isnull(sh.serversOutDtime,sh.modidate) serversOutDtime,sh.serviceType
        from shouhou sh with(nolock) where sh.xianshi = 1 and sh.imei = #{imei}
        <if test="startTime != null and endTime != null">
            and isnull(sh.serversOutDtime,sh.modidate) between #{startTime} and #{endTime}
        </if>
        <if test="serviceType != null">
            and sh.serviceType = #{serviceType}
        </if>
        order by sh.id desc
    </select>
    <select id="getRecoverSubPayTime" resultType="java.time.LocalDateTime">
        select top 1 s.pay_time from dbo.recover_basket b with(nolock)
                                         left join dbo.recover_sub s with(nolock) on b.sub_id = s.sub_id
        where isnull(b.isdel,0)= 0 and b.imei =#{imei} and s.sub_check = 3 order by s.pay_time desc
    </select>
    <select id="getBbsxpUser" resultType="com.jiuji.oa.afterservice.bigpro.po.BbsxpUsers">
        SELECT top 1 ID, UserName, UserPass, mobile, tel, UserMail, UserSex, UserLastIP, UserRegTime, UserLandTime, UserBuyTime
             , realname, userclass, totalpoint, points, chengzhangzhi, blacklist, comment, inuser, istemp, area, kinds
             , erdu, save_money, hezuo, hezuo_name, lastarea, birthday, areaid, LastAreaId, salt, AgeGroup, HasChild
             , ChildSex, ChildAgeGroup, uCoin, LastLoginClient, regClient, cityid, isValidate, EffectM, payPwd, saltPay
             , UserBuyTime1, LastAreaId1, frozenMoney, xtenant, specialType, iaMarry, occupation, hasCar, isBigCustomer
             , userNature, visitTime, hasNoticedWay, blackListType, headImg, wxHeadImg, firstAppLoginTime, UserLastLandTime
             , UserAppLandtime, bindGroupId, userTypes,case ID when #{userId} then 1 else 2 end rank
        FROM dbo.BBSXP_Users with(nolock) where xtenant = #{xTenant} and (ID = #{userId} or mobile = #{mobile})
        order by rank asc
    </select>

    <select id="getFilmByImei" resultType="java.lang.Integer">
        select TOP 1 b.basket_id
        from dbo.basket b with(nolock)
        left join dbo.sub s with(nolock) on b.sub_id=s.sub_id
        left join dbo.productinfo p with(nolock) on b.ppriceid=p.ppriceid
        left join dbo.basketBindRecord r with(nolock) on r.basket_id = b.basket_id
        where isnull(b.isdel,0)=0 and s.sub_check=3 and r.imei='6974264611262'
          and isnull(b.type,0) not in (85,86)
          and exists(select 1 from dbo.f_category_children('219,465,386,220,662') f where f.ID=p.cid )
    </select>

    <select id="getYaDingRegisterState" resultType="java.lang.Integer">
        SELECT
            register_state
        from
            yading_service_state yss WITH(nolock)
        where
            id = #{serviceRecordId}
    </select>
    <select id="getYaDingRegisterStateNew" resultType="java.lang.Integer">
    SELECT s.register_state FROM ServiceRecord sr with(nolock)
                left join yading_service_record r  with(nolock ) on sr.basket_id = r.basket_id
            left join yading_service_state s with(nolock) on r.id = s.id and s.is_delete =0
        where r.is_deleted = 0 and sr.id = #{serviceRecordId}
    </select>
    <select id="getStopOrderId" resultType="java.lang.Integer">
        <choose>
            <when test="businessType == 1">
                <!--新机单-->
                SELECT s.sub_id FROM sub s with(nolock)
                where exists(SELECT 1 from basket b with(nolock) where b.sub_id = s.sub_id and b.basket_id = #{discountBasketId})
            </when>
            <when test="businessType == 6">
                <!--回收单-->
                SELECT rs.sub_id FROM recover_sub rs with(nolock)
                where exists(SELECT 1 from recover_basket rb with(nolock) where rb.sub_id = rs.sub_id and rb.id = #{discountBasketId})
            </when>
        </choose>
    </select>
    <select id="getHuiJiBaoRegisterState" resultType="java.lang.Integer">
        select register_state from huiJiBao_service_state where id=#{basketId}
    </select>
    <select id="getHuiJiBaoRegisterStateNew" resultType="java.lang.Integer">
        select h.register_state
        from dbo.huiJiBao_service_state h with (nolock)
         left join dbo.ServiceRecord s with (nolock) on h.id = s.basket_id
        WHERE ISNULL(s.isdel,0) = 0 and s.id = #{serviceRecordId}
    </select>
</mapper>
