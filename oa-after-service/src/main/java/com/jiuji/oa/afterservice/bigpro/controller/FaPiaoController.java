package com.jiuji.oa.afterservice.bigpro.controller;


import com.jiuji.oa.afterservice.api.po.TaxPiao;
import com.jiuji.oa.afterservice.bigpro.service.FaPiaoService;
import com.jiuji.oa.afterservice.bigpro.vo.res.TaxPiaoInfo;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * <p>
 * 发票控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-19
 */
@RestController
@RequestMapping("/api/bigpro/faPiao")
@Api(tags = "退换机管理: 发票接口")
public class FaPiaoController {

    @Autowired
    private FaPiaoService faPiaoService;

    /**
     * 更改退换发票金额
     *
     * @param id    售后退换id
     * @param price 金额
     **/
    @PutMapping("/updatePiaoPrice")
    @ApiOperation(value = "更改退换发票金额")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "售后退换id", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "price", value = "金额", paramType = "query", dataType = "BigDecimal")})
    public R<String> updatePiaoPrice(Integer id, BigDecimal price) {
        return faPiaoService.updatePiaoPrice(id, price);
    }

    /**
     * 获取售后退换发票微信二维码（发票微信退款验证）
     *
     * @param id 售后退换id
     **/
    @GetMapping("/getShouhouPayFaPiaoCode/{id}")
    @ApiOperation(value = "获取售后退换发票微信二维码")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "售后退换id", paramType = "path", dataType = "Integer")})
    public R<String> getShouhouPayFaPiaoCode(@PathVariable Integer id) {
        return faPiaoService.getShouhouPayFaPiaoCode(id);
    }

    /**
     * 获取发票信息
     *
     **/
    @GetMapping("/getPiaoInfo")
    @ApiOperation(value = "获取发票信息")
    public R<TaxPiao> getPiaoInfo(Long subId, Long userId, String tradedate) {
        //todo
        return faPiaoService.getPiaoInfo(subId, null, userId,tradedate);
    }
    /**
     *
     * @param subId
     * @param type 订单类型 0 订单 2 良品单 1 维修
     * @param userId
     * @param tradedate
     * @return
     */
    @GetMapping("/getPiaoInfo/v3")
    @ApiOperation(value = "获取发票信息")
    public R<TaxPiao> getPiaoInfoV3(Long subId,Integer type, Long userId, String tradedate) {
        //todo
        return faPiaoService.getPiaoInfo(subId,type,userId,tradedate);
    }

    @GetMapping("/getShouhouPiaoInfo")
    @ApiOperation(value = "获取售后发票信息")
    public R<TaxPiaoInfo> getPiaoInfo(Integer shouhouId) {
        return faPiaoService.getShouhouPiaoInfo(shouhouId);
    }

}

