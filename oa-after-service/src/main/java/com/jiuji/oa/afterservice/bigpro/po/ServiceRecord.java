package com.jiuji.oa.afterservice.bigpro.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 不同步迁移历史库
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ServiceRecord")
public class ServiceRecord extends Model<ServiceRecord> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer basketId;

    private Integer subId;

    @TableField("basket_idBind")
    private Integer basketIdbind;

    private String area;

    private LocalDateTime tradedate;

    private String imei;
    /**
     * @see com.jiuji.cloud.after.enums.JiujiServiceTypeEnum 旧的
     * @see com.jiuji.cloud.after.enums.ServiceEnum 新的
     */
    @TableField("ServiceType")
    @ApiModelProperty("1 意外一年 2 意外二年 3 延保一年 6:延保2年 4:电池保两年 5 碎屏保一年 7 进水保 9 碎屏保2年 8:屏背保一年  10屏背保两年 11 以换代修九机服务 12 意外保半年 13 碎屏保半年")
    private Integer serviceType;

    private Integer userid;

    private Boolean isdel;

    private Double price;

    private Double feiyong;

    @TableField("discountBasketId")
    private Integer discountBasketId;

    @ApiModelProperty("商品ppid")
    private Integer ppriceid;

    private Integer areaid;

    @TableField("servicesTypeBindId")
    private Integer servicesTypeBindId;

    @ApiModelProperty(value = "服务生效开始时间")
    @TableField("startTime")
    private LocalDateTime startTime;
    @ApiModelProperty(value = "服务生效结束时间")
    @TableField("endTime")
    private LocalDateTime endTime;
    @ApiModelProperty(value = "服务生效结束时间")
    @TableField("server_shouhou_id")
    private Integer serverShouhouId;


    /**
     * 出险次数
     */
    @TableField("service_count")
    private Integer serviceCount;
    /**
     * @see com.jiuji.cloud.after.enums.ServiceClassificationEnum
     */
    @ApiModelProperty(value = "服务分类 (1 自营 2 九讯)")
    @TableField("classification")
    private Integer classification;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
