package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.cloud.after.vo.res.ReceiveSendResVO;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouHuishouSumDataBo;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouHuishou;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouHuiShouReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouHuishouListReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouHouHuishouListRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouPjInfoRes;
import com.jiuji.oa.afterservice.other.bo.DiaoboToWxBO;
import com.jiuji.oa.afterservice.smallpro.recover.vo.req.*;
import com.jiuji.oa.afterservice.smallpro.recover.vo.res.ReceiveSendRes;
import com.jiuji.oa.afterservice.smallpro.recover.vo.res.RecoverProBindToolRes;
import com.jiuji.oa.afterservice.smallpro.recover.vo.res.SmallProRecoverListQueryRes;
import com.jiuji.oa.oacore.common.req.PageReq;
import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.tc.common.vo.R;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface ShouhouHuishouService extends IService<ShouhouHuishou> {
    /**
     * 保存售后回收
     * @param shouhouHuishou
     * @return
     */
    Boolean saveShouhouHuishou(ShouhouHuishou shouhouHuishou);

    PageRes<ShouhouHuishou> pageHuishouToWx(PageReq req);

    PageRes<DiaoboToWxBO> pageDiaoboToWx(PageReq req);

    /**
     * 售后维修配件 回收、换货、返还
     * @param req
     * @return
     */
    R<Boolean> addHuiShou(ShouhouHuiShouReq req);

    /**
     * 售后配件回收列表
     * @param req
     * @returnhouhou
     */
    ShouHouHuishouListRes getHuishouList(ShouhouHuishouListReq req);

    /**
     * 查询售后维修配件统计结果数据
     * @return
     */
    ShouhouHuishouSumDataBo getHuiSouSumData(ShouhouHuishouListReq param, Integer loginArea);

    /**
     * 取指定售后ID回收配件
     * @param shouhouId
     * @return
     */
    List<ShouhouHuishou> getHuishouListBy(Integer shouhouId);

    /**
     * 查询售后配件回收详情
     * @param shouhouId shouhouId
     * @return
     */
    Map<Integer,List<ShouhouHuishou>> getHuishouMapByshouhouIds(List<Integer> shouhouId);


    /**
     * 查询售后维修配件、回收配件信息
     * @param shouhouId
     * @return
     */
    ShouhouPjInfoRes getShouhouPjInfoById(Integer shouhouId);

    /**
     * 旧件审核
     * @param id
     * @return
     */
    R<String> huiShouCheck(Integer id);

    /**
     * 回收配件撤销
     * @param id
     * @return
     */
    R<String> huishouPjDel(Integer id);

    /**
     * 回收旧件列表查询接口
     * @param req
     * @return
     */
    R<SmallProRecoverListQueryRes> getHuishouList(SmallProRecoverListQueryReq req);

    /**
     * 置换旧件、批量确认
     * C# 方法zhihuanpiliang    PiliangConfirmZhihuan
     * @param shouhouIds
     * @return
     */
    R<Boolean> exchangeBatchConfirm(List<Integer> shouhouIds);

    /**
     * 批量回收转置换
     * C# uptozhihuan    pluptozh
     * @param shouhouIds
     * @return
     */
    R<Boolean> recoverTransferToExchangeBatch(List<Integer> shouhouIds);

    /**
     * 转回收
     * C# zhunhuishou    zhunhuishou
     * @param recoverId
     * @return
     */
    R<Boolean> transferToRecover(Integer recoverId);

    /**
     * 回收类型修改接口
     * @param req
     * @return
     */
    R<Boolean> updateRecoverKind(RecoverSetKindReq req);

    /**
     * 修改预售渠道
     * @param req
     * @return
     */
    R<Boolean> updatePreSaleChannel(RecoverSetPreSaleChannelReq req);

    /**
     * 旧件销售审核确认出售
     * @param req
     * @return
     */
    R<Boolean> recoverSaleBatchConfirm(RecoverSaleBatchConfirmReq req);

    /**
     * 回收旧件压屏操作
     * @param req
     * @return
     */
    R<Boolean> recoverSqueezeBatch(RecoverSqueezeBatchReq req);

    /**
     * 获取回收旧件压屏耗材绑定
     * @param hsId
     * @return
     */
    R<RecoverProBindToolRes> getRecoverProBindToolList(Integer hsId);

    /**
     * 旧件销售提交(待出售状态) -批量
     * @param req
     * @return
     */
    R<Boolean> recoverBatchSale(RecoverBatchSaleReq req);

    /**
     * 扫码收发货操作
     *
     * @param wType
     * @param receiveType
     * @param areaId
     * @param qrCode
     * @return
     */
    R<ReceiveSendRes> saveReceiveSendByQrCode(Integer wType, Integer receiveType, Integer areaId, String qrCode);

    /**
     * 查询收发货列表
     *
     * @param wType
     * @param receiveType
     * @param areaId
     * @return
     */
    R<ReceiveSendRes> listReceiveSend(Integer wType, Integer receiveType, Integer areaId, String wuliuId);

    /**
     * 提交收发货操作
     * @param req
     * @return
     */
    R<Boolean> saveReceiveSend(ReceiveSendRes req);

    /**
     * 旧件待办
     * @param areaId
     * @return
     */
    R<ReceiveSendResVO> listReceiveSendCount(Integer areaId);
}
