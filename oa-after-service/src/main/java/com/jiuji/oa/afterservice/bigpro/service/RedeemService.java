package com.jiuji.oa.afterservice.bigpro.service;

import com.jiuji.oa.afterservice.bigpro.po.MemberPointExchangeCouponModel;
import com.jiuji.oa.afterservice.bigpro.po.PointExchangeCouponOutput;
import com.jiuji.oa.afterservice.bigpro.vo.PointExchangeMaxCouponReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.tc.common.vo.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface RedeemService {

    /**
     * 模拟登录
     * @param areaId
     */
    void simulateLogin(Integer areaId);

    /**
     * 判断是否使用积分兑换券
     * @return
     */
    Boolean isUseRedeem();


    PointExchangeMaxCouponRes pointExchangeMaxCoupon(PointExchangeMaxCouponReq req);

    /**
     * 判断优惠码是否可用
     * @param req
     * @return
     */
    List<CanUseCouponOutputRes> CanUseCoupon(CanUseCouponOutputReq req);


    /**
     * 前端优惠码查询
     * @param req
     * @return
     */
    MemberPointExchangeRes getMemberPointExchangeCouponByUserId(MemberPointExchangeCouponReq req);

    /**
     * 主站查询维修单可以用优惠码
     * @param req
     * @return
     */
    List<YouHuiMaBySubRes> getYouHuiMaBySub( YouHuiMaBySubReq req);

    /**
     * 最优推荐
     * @param shouHouId
     * @return
     */
    List<GetPreferentialRecommendationOutputRes> getPreferentialRecommendation( Integer shouHouId);


    /**
     * 积分兑换优惠券
     * @param req
     * @return
     */
    PointExchangeCouponOutput pointExchangeCoupon(PointExchangeCouponOutputReq req);


    /**
     * 撤销优惠券
     * @param req
     * @return
     */
    R cancelExchangeCoupon(CancelExchangeCouponOutputReq req);


    /**
     * 一键使用
     * @param req
     */
    void oneClickUse(OneClickUseReq req);
}
