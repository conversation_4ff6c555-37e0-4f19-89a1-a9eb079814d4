package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.oa.afterservice.bigpro.bo.RepairFeeBo;
import com.jiuji.oa.afterservice.bigpro.bo.RepairPriceBo;
import com.jiuji.oa.afterservice.bigpro.dao.RepairMapper;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.service.RepairService;
import com.jiuji.oa.afterservice.bigpro.service.ShouHouPjService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.tc.common.vo.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;


/**
 * @author: gengjiaping
 * @date: 2020/3/10
 */
@Service
public class RepairServiceImpl implements RepairService {

    @Autowired
    private RepairMapper repairMapper;
    @Lazy
    @Autowired
    private ShouhouService shouhouService;

    @Override
    public R<Boolean> updateShouhouFeiyong(Integer wxid) {
        RepairPriceBo repairPriceBo = getShouhouFeiyong(wxid);
        if(repairPriceBo == null){
            return R.error("获取维修费用失败");
        }
        UpdateWrapper<Shouhou> shouhouUpdateWrapper = new UpdateWrapper<>();
        shouhouUpdateWrapper.lambda().set(Shouhou::getFeiyong,repairPriceBo.getRepairFee())
                .set(Shouhou::getCostprice,repairPriceBo.getCostPrice())
                .set(Shouhou::getServiceCostprice,repairPriceBo.getServiceCostPrice())
                .eq(Shouhou::getId,wxid);
        boolean res=shouhouService.update(shouhouUpdateWrapper);
        return R.success(res);
    }

    @Override
    public RepairPriceBo getShouhouFeiyong(Integer wxid) {
        RepairPriceBo repairPriceBo = new RepairPriceBo();
        List<RepairFeeBo> repairFeeList=repairMapper.queryRepaireFee(wxid);
        BigDecimal repairFee=BigDecimal.ZERO;
        BigDecimal costPrice=BigDecimal.ZERO;
        BigDecimal serviceCostPrice = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(repairFeeList)){
            RepairFeeBo repairFeeBo = repairFeeList.get(0);
            if(repairFeeBo != null){
                repairFee=repairFeeBo.getFeiYong();
                costPrice=repairFeeBo.getCostPrice();
                serviceCostPrice = repairFeeBo.getServiceCostPrice();
            }

        }

        //查询维修优惠码使用情况
        BigDecimal youHuiMaTotal = SpringUtil.getBean(ShouHouPjService.class).getShouhouYouhuimaTotal(wxid,repairFee);

        if (youHuiMaTotal.compareTo(BigDecimal.ZERO)>0){
            repairFee = repairFee.subtract(youHuiMaTotal);
        }
        if (repairFee.compareTo(BigDecimal.ZERO)<0){
            repairFee=BigDecimal.ZERO;
        }
        repairPriceBo.setCostPrice(costPrice);
        repairPriceBo.setRepairFee(repairFee);
        repairPriceBo.setServiceCostPrice(serviceCostPrice);
        return repairPriceBo;
    }


}
