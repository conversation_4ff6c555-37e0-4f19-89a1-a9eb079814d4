package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.bo.HexiaoBo;
import com.jiuji.oa.afterservice.bigpro.bo.WxPeiJianBo;
import com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput;
import com.jiuji.oa.afterservice.bigpro.vo.res.WeiXiuPjRes;
import com.jiuji.tc.common.vo.R;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-31
 */
public interface WxkcoutputService extends IService<Wxkcoutput> {

    /**
     * 回收，返还的旧件的分类ID
     * @param xTenant
     * @return
     */
    List<Integer> selectRecoveryReturnCidList(Integer xTenant);
    /**
     * 获取维修配件信息
     * @return
     */
    List<HexiaoBo> getHexiao(Integer wxId);

    /**
     * 检查是否存在售后服务
     * @param ppid
     * @param shoushouId
     * @returnget
     */
    Boolean checkExistShouhouService(Integer ppid,Integer shoushouId);

    /**
     * 保存维修库存
     * @param wxkcoutput
     * @return
     */
    Boolean saveWxkcoutput(Wxkcoutput wxkcoutput);

    /**
     * 查询维修配件
     * @param shouhouId
     * @return
     */
    R<List<WeiXiuPjRes>> getWeixiuPJ(Integer shouhouId);

    /**
     * 查询出库库存id
     * @param wxId
     * @return
     */
    Integer getKcIdByWxId(Integer wxId);

    /**
     * 查询维修库存条数
     * @param shouhouId
     * @return
     */
    Integer getWxKcCount(Integer shouhouId);

    /**
     *
     * @param shouhouId
     * @param limitIdList
     * @return
     */
    List<Integer> cidCheck(Integer shouhouId, List<Integer> limitIdList);

    /**
     *
     * @param shouhouId
     * @param limitIdList
     * @return
     */
    List<Integer> cidCheck1(Integer shouhouId, List<Integer> limitIdList);

    /**
     * 根据售后id查询维修配件信息
     * @param shouhouId
     * @return
     */
    List<WxPeiJianBo> getWxPeijianByShouhouId(Integer shouhouId);

    /**
     * 维修单采购配件入库时自动出库
     * C# 方法 weixiuDinggouPeijianLock
     *
     * @param transferId
     * @param orderType
     * @return
     */
    R<Boolean> autoOutStockWhenCaiGouInStock(Integer transferId, Integer orderType);

    /**
     * 获取维修单信息
     * @param id
     * @param xTenant
     * @param areaId
     * @param hasAuthPart
     * @param authorizeId
     * @return
     */
    Wxkcoutput getByIdAndXtenantAndAuthPart(Integer id, Integer xTenant, Integer areaId, boolean hasAuthPart, Integer authorizeId);

    /**
     * 获取优惠分摊的配件
     * @param shouhouId
     * @param wxkcIds
     * @param shouhouServicesPpriceids  服务类的ppid不参与分摊
     * @return
     */
    List<Wxkcoutput> listYouhuiMaAllocation(Integer shouhouId, Set<Integer> wxkcIds, List<Integer> shouhouServicesPpriceids);

    /**
     * 批量更新配件的分摊费用
     * @param shouhouId
     * @param wxkcoutputs
     * @return
     */
    int updateYouHuiFeiYongBatch(Integer shouhouId, String youHuiMa, List<Wxkcoutput> wxkcoutputs);

    /**
     * 获取撤销配件分摊的优惠码费用
     * @param wxId
     * @return
     */
    BigDecimal sumCancelPjYouHuiFeiyong(Integer wxId);

    /**
     *
     * @param servicePjIds
     * @param serviceType
     * @return
     */
    int updateServiceType(List<Integer> servicePjIds, Integer serviceType);

    /**
     * 取消指定的服务类型
     * @param wxId
     * @param serviceType
     * @return
     */
    int cancelServiceType(Integer wxId, Integer serviceType);
}
