package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.po.Daiyonjiyuyueinfo;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-26
 */
public interface DaiyonjiyuyueinfoService extends IService<Daiyonjiyuyueinfo> {
    /**
     * 判断当前维修单是否预约过代用机
     * @param shouhouId
     * @return
     */
    Boolean isYuyueDaiyonji(Integer shouhouId);
}
