package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.ch999.common.util.vo.Result;
import com.google.common.collect.Maps;
import com.jiuji.cloud.after.enums.*;
import com.jiuji.cloud.after.vo.req.CutScreenShouhouReq;
import com.jiuji.cloud.after.vo.req.PjtShouhouReq;
import com.jiuji.cloud.after.vo.res.PjtShouhouRes;
import com.jiuji.cloud.huishou.service.ProductSupportCloud;
import com.jiuji.cloud.huishou.vo.response.ProductSupportCloudVo;
import com.jiuji.cloud.product.service.ProductCloud;
import com.jiuji.cloud.product.vo.request.BargainLinkReqVO;
import com.jiuji.cloud.product.vo.response.BargainLinkVO;
import com.jiuji.cloud.stock.service.WuliuStockCloud;
import com.jiuji.cloud.stock.vo.request.WuLiuUpdateReqV2;
import com.jiuji.oa.afterservice.api.bo.Ch999UserBasicBO;
import com.jiuji.oa.afterservice.api.dao.TaxPiaoMapper;
import com.jiuji.oa.afterservice.api.po.TaxPiao;
import com.jiuji.oa.afterservice.api.service.TaxPiaoService;
import com.jiuji.oa.afterservice.apollo.ApolloEntity;
import com.jiuji.oa.afterservice.apollo.ShouhouTestInfoConfig;
import com.jiuji.oa.afterservice.bigpro.bo.*;
import com.jiuji.oa.afterservice.bigpro.bo.discount.DiscountInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.discount.UseDiscountBo;
import com.jiuji.oa.afterservice.bigpro.bo.externalrepair.SimpleServiceSubInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.productkc.OperateProductKcRes;
import com.jiuji.oa.afterservice.bigpro.bo.servicerecord.UseServiceRecordBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouHouOtherCostPriceByPPriceidMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouHouPjMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouExMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouMapper;
import com.jiuji.oa.afterservice.bigpro.entity.EmployeeOrders;
import com.jiuji.oa.afterservice.bigpro.entity.Productbarcode;
import com.jiuji.oa.afterservice.bigpro.entity.WxProductServiceOpeningType;
import com.jiuji.oa.afterservice.bigpro.enums.*;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.repository.RecoverMkcNewLogsRepository;
import com.jiuji.oa.afterservice.bigpro.repository.ShouhouLogNewRepository;
import com.jiuji.oa.afterservice.bigpro.repository.document.RecoverMkcNewLogs;
import com.jiuji.oa.afterservice.bigpro.repository.document.ShouhouLogNew;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.statistics.enums.WuLiuType;
import com.jiuji.oa.afterservice.bigpro.statistics.po.Wuliu;
import com.jiuji.oa.afterservice.bigpro.statistics.service.WuliuService;
import com.jiuji.oa.afterservice.bigpro.vo.*;
import com.jiuji.oa.afterservice.bigpro.vo.req.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.bigpro.wrapper.ShouhouWrapper;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.cloud.vo.ProRelateInfoService;
import com.jiuji.oa.afterservice.cloud.vo.ProductServiceOpeningBO;
import com.jiuji.oa.afterservice.cloud.vo.ProductServiceOpeningVO;
import com.jiuji.oa.afterservice.cloud.vo.web.RepairBuyCouponVO;
import com.jiuji.oa.afterservice.cloud.vo.web.SubCheckChangedParam;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.properties.ImageProperties;
import com.jiuji.oa.afterservice.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.afterservice.common.config.rabbimq.RabbitMqConfig;
import com.jiuji.oa.afterservice.common.constant.*;
import com.jiuji.oa.afterservice.common.enums.ShouhouOrderTypeEnum;
import com.jiuji.oa.afterservice.common.enums.*;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.source.ConstantsSource;
import com.jiuji.oa.afterservice.common.source.InwcfUrlSource;
import com.jiuji.oa.afterservice.common.source.MoaUrlSource;
import com.jiuji.oa.afterservice.common.source.WwwUrlSource;
import com.jiuji.oa.afterservice.common.util.*;
import com.jiuji.oa.afterservice.common.vo.PageVo;
import com.jiuji.oa.afterservice.common.vo.res.OaQuequRes;
import com.jiuji.oa.afterservice.config.po.ShouhouPpidBind;
import com.jiuji.oa.afterservice.config.service.ShouhouPpidBindService;
import com.jiuji.oa.afterservice.customeraccount.service.CustomerAccountService;
import com.jiuji.oa.afterservice.log.service.ShouhouLogNewService;
import com.jiuji.oa.afterservice.other.bo.AreaBelongsDcHqD1AreaId;
import com.jiuji.oa.afterservice.other.bo.OtherShouYinBo;
import com.jiuji.oa.afterservice.other.enums.OtherShouYinTypeEnum;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.other.service.*;
import com.jiuji.oa.afterservice.other.vo.res.ZhonyiRes;
import com.jiuji.oa.afterservice.refund.service.way.ThirdOriginWayService;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceVO;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouBasicInfo;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouRepairInfoRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.WxPjBasicInfo;
import com.jiuji.oa.afterservice.smallpro.constant.SmallProRelativePathConstant;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproWxMapper;
import com.jiuji.oa.afterservice.smallpro.enums.BasketTypeEnum;
import com.jiuji.oa.afterservice.smallpro.enums.JiujiTenantEnum;
import com.jiuji.oa.afterservice.smallpro.vo.res.TransferUserRes;
import com.jiuji.oa.afterservice.stock.enums.ESubCheckEnum;
import com.jiuji.oa.afterservice.stock.po.MkcDellogs;
import com.jiuji.oa.afterservice.stock.service.IRecoverMkcService;
import com.jiuji.oa.afterservice.stock.service.MkcDellogsService;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.oa.afterservice.stock.service.ProductMkcService;
import com.jiuji.oa.afterservice.stock.service.impl.ProductKcServiceImpl;
import com.jiuji.oa.afterservice.sub.po.Category;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.afterservice.sys.service.AuthConfigService;
import com.jiuji.oa.afterservice.sys.service.RetryService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.nc.SmsConfigCloud;
import com.jiuji.oa.nc.sms.SmsConfigCodeEnum;
import com.jiuji.oa.nc.sms.SmsConfigVO;
import com.jiuji.oa.nc.sms.SmsPushMethodEnum;
import com.jiuji.oa.office.applyinfo.client.ApplyInfoClient;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.req.MemberReq;
import com.jiuji.oa.orginfo.member.res.MemberBasicRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.common.vo.ResultCodeEnum;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SignConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.constants.TimeFormatConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BooleanSupplier;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@Slf4j
@Service
public class ShouhouServiceImpl extends ServiceImpl<ShouhouMapper, Shouhou> implements ShouhouService {


    /**
     * 现货维修单 userid
     */
    public static final Long SPOT_GOODS_USER_ID=76783L;
    private static final String RECOVER_MKC_LOG_ACT = "recoverMkcLog";
    private static final Integer REPAIR_SERVICE_CID = 481;
    @Autowired
    private RabbitTemplate oaAsyncRabbitTemplate;

    @Resource
    private ZhiWuService zhiWuService;
    @Resource
    private ShouHouCantFixReasonService cantFixReasonService;
    @Resource
    private WebCloud webCloud;
    @Resource
    private MkcDellogsService mkcDellogsService;
    @Autowired
    private ProductinfoService productinfoService;
    @Autowired
    private ShouhouYuyuelockppidsService yuyuelockppidsService;
    @Autowired
    private ProductKcService productKcService;
    @Resource
    private ConstantsSource constantsSource;
    @Autowired
    private AttachmentsService attachmentsService;
    @Resource
    private MemberClient memberClient;
    @Resource
    private ShouhouLogNewRepository shouhouLogNewRepository;
    @Resource
    private RecoverMkcNewLogsRepository recoverMkcNewLogsRepository;
    @Resource
    private ShouhouReturncbService shouhouReturncbService;
    @Resource
    private ShouhouTimePointService shouhouTimePointService;
    @Resource
    private IWxProductServiceOpeningTypeService wxProductServiceOpeningTypeService;
    @Autowired
    @Lazy
    private ShouhouYuyueService shouhouYuyueService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private ShouhouOtherService shouhouOtherService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Autowired
    private ZhongyouApplyService zhongyouApplyService;
    @Autowired
    private ShouhoutestInfoService shouhoutestInfoService;
    @Autowired
    private CaigouBasketRefShouhouService caigouBasketRefShouhouService;
    @Autowired
    private SmallproWxMapper smallproWxMapper;
    @Autowired
    @Lazy
    private ProductMkcService productMkcService;
    @Resource
    private UserInfoClient userInfoClient;
    @Autowired
    private WxkcoutputService wxkcoutputService;
    @Autowired
    private NahuoduilieService nahuoduilieService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private NumberCardService numberCardService;
    @Autowired
    private ShouhouMsgconfigService shouhouMsgconfigService;
    @Resource
    private WwwUrlSource wwwUrlSource;
    @Resource
    private InwcfUrlSource inwcfUrlSource;
    @Autowired
    private ShouhouMsgService shouhouMsgService;
    @Autowired
    private ShouhouQudaoService shouhouQudaoService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private WeixinUserService weixinUserService;
    @Autowired
    private ShouhouTimerService shouhouTimerService;
    @Autowired
    private ShouhouSendaddressService shouhouSendaddressService;
    @Autowired
    private YuyueLogsService yuyueLogsService;
    @Autowired
    private ShouhouServiceConfigService shouhouServiceConfigService;
    @Autowired
    private ShoushouPjpriceConfigService shoushouPjpriceConfigService;
    @Autowired
    private ShouhouApplyService shouhouApplyService;
    @Autowired
    private ShouhouHexiaoService shouhouHexiaoService;
    @Autowired
    private ShouhouHuishouService shouhouHuishouService;
    @Autowired
    private RepairService repairService;
    @Autowired
    private ShouhouPpidDictService shouhouPpidDictService;
    @Autowired
    private ShouHouOtherCostPriceByPPriceidMapper shouHouOtherCostPriceByPPriceidMapper;
    @Autowired
    private ShouhouTuihuanService shouhouTuihuanService;
    @Autowired
    private TuihuanConfigService tuihuanConfigService;
    @Autowired
    private ShouyinOtherService shouyinOtherService;
    @Autowired
    private ShouhouLogsService shouhouLogsService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Autowired
    private ShouhouRomUpgradeService shouhouRomUpgradeService;
    @Autowired
    private ShouhouTixingService shouhouTixingService;
    @Autowired
    private SubCollectionService subCollectionService;
    @Autowired
    private ShouhouBusinessinfoService shouhouBusinessinfoService;
    @Autowired
    @Lazy
    private ShouhouExService shouhouExService;
    @Autowired
    private WeixiuzuKindService weixiuzuKindService;
    @Autowired
    private ShouhouFuwupicService shouhouFuwupicService;
    @Autowired
    private WxconfigoptionService wxconfigoptionService;
    @Autowired
    private WeixiuzulogsService weixiuzulogsService;
    @Autowired
    private AddinfopsService addinfopsService;
    @Resource
    private MoaUrlSource moaUrlSource;
    @Autowired
    private ShouHouPjService shouHouPjService;
    @Autowired
    private ShouhouExMapper shouhouExMapper;
    @Resource
    private ImageProperties imageProperties;
    @Autowired
    private ShouhouConstants shouhouConstants;
    @Autowired
    private Executor pushMessageExecutor;
    @Resource
    private ApplyInfoClient applyInfoClient;
    @Autowired
    private SysConfigService sysConfigService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Autowired
    private SmallproMapper smallproMapper;
    @Autowired
    private SubReceptionService subReceptionService;
    @Resource
    private JiujiSystemProperties jiujiSystemProperties;
    @Autowired
    private TApplyinfoService tApplyinfoService;
    @Autowired
    private ShouhouLogNewService shouhouLogNewService;
    @Autowired
    private AuthConfigService authConfigService;
    @Autowired
    private AreainfoService areainfoService;
    @Autowired
    private ShouhouPpidBindService shouhouPpidBindService;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Resource
    private ServiceRecordService serviceRecordService;
    @Autowired
    private ShouhouMemberDiscountService memberDiscountService;
    @Resource
    private ProductKcServiceImpl productKcServiceImpl;
    @Resource
    @Lazy
    private ShouhouService shouhouService;
    @Resource
    private DiaoboSubService diaoboSubService;
    @Resource
    private ThirdPlatStatusSyncService thirdPlatStatusSyncService;

    @Resource
    private IRecoverMkcService recoverMkcService;
    @Resource
    private RecoverMarketinfoService recoverMarketinfoService;
    @Resource
    private IRecoverMarketsubinfoService recoverMarketsubinfoService;
    @Resource
    private ProductCloud productCloud;
    @Resource
    private ServiceFeeConfigService feeConfigService;

    @Resource
    private ShouhouTestInfoConfig shouhouTestInfoConfig;
    @Resource
    private SmsConfigCloud smsConfigCloud;
    @Resource
    private ApolloEntity apolloEntity;

    private String ORDINARY_OUT_SERVICE = "出险：";
    private String GAOJI_RANK_OUT_SERVICE = "(高级授权)出险：";
    private String OUT_SERVICE_IMIE_STRAT = "出险串号[";
    private String OUT_SERVICE_IMIE_END = "]";

    @Resource
    private ProductbarcodeService barcodeService;
    /**
     * 重大办标记
     */
    public final static String IS_ZDB = "isZdb";

    @Override
    public IPage<WxRecordBo> getWxRecord(Page<WxRecordBo> page, Integer userId) {
        if (userId == null || userId == 0) {
            return null;
        }
        baseMapper.getWxRecord(page,userId);
        List<WxRecordBo> list = page.getRecords();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(e -> {
                String tuihuanKindName;
                if(Boolean.TRUE.equals(e.getIssoft())){
                    tuihuanKindName = "软件";
                }else{
                    tuihuanKindName = ObjectUtil.defaultIfNull(EnumUtil.getMessageByCode(TuihuanKindEnum.class, e.getTuihuanKind()), "维修");
                }
                e.setTuihuanKindName(tuihuanKindName);
            });
        }
        return page;
    }

    @Override
    public List<HardwareHistoryRecordBo> getHardwareHistoryRecords(String imei) {
        if (StringUtils.isEmpty(imei)) {
            return null;
        }
        return baseMapper.getHardwareHistoryRecords(imei);
    }

    @Override
    public R<List<ShouhouYuyuePeijianInfoRes>> getPeijian(YuyuePeijianAddReq yuyuePeijianAddReq) {
        List<ShouhouYuyuePeijianInfoRes> peijianInfoResList = null;
        try {
            Integer ppid = yuyuePeijianAddReq.getPpid();
            Integer pid = yuyuePeijianAddReq.getProductId();
            if (ppid == null || ppid == 0 || pid == 0 || pid == null) {
                //通过串号查询
                ProductImeiBo productImeiBo = baseMapper.getProductInfoByImei(yuyuePeijianAddReq.getImei());
                if (productImeiBo != null) {
                    ppid = productImeiBo.getPpid();
                    pid = productImeiBo.getPid();
                }
            }
            String host = sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.M_URL, Math.toIntExact(Namespaces.get()));
            String url = host + String.format(UrlConstants.GET_YUYUE_PEIJIAN, pid, ppid, yuyuePeijianAddReq.getRepairClassifyId(), LocalDateTime.now());
            String json = HttpClientUtil.get(url);
            if (StringUtils.isNotEmpty(json)) {
                R<List<WxPeijianInfoBo>> wxpeijianRes = JSON.parseObject(json, new TypeReference<R<List<WxPeijianInfoBo>>>() {
                });
                if (ResultCode.SUCCESS == wxpeijianRes.getCode() && wxpeijianRes.getData() != null) {
                    List<Integer> ppids = wxpeijianRes.getData().stream().map(e -> e.getPpid()).collect(Collectors.toList());

                    Map<Integer, Productinfo> productMap = productinfoService.getProductMapByPpids(ppids);
                    peijianInfoResList = wxpeijianRes.getData().stream().map(e -> {
                        ShouhouYuyuePeijianInfoRes peijianInfoRes = new ShouhouYuyuePeijianInfoRes();
                        BeanUtils.copyProperties(e, peijianInfoRes);
                        if (productMap.containsKey(e.getPpid())) {
                            Productinfo product = productMap.get(e.getPpid());
                            peijianInfoRes.setProductColor(product.getProductColor());
                            peijianInfoRes.setProductName(product.getProductName());
                        }
                        return peijianInfoRes;
                    }).collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            log.error("获取配件信息异常:{}", e.getMessage());
            return R.error("获取配件信息异常");
        }
        return R.success("获取成功", peijianInfoResList);
    }

    @Override
    public SuperiorProductsLinkRes getSuperiorProductsLink(SuperiorProductsLinkReq req) {
        //判断一下是否为M版
        BargainLinkReqVO vo = new BargainLinkReqVO();
        Optional.ofNullable((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .map(ServletRequestAttributes::getRequest).ifPresent(item->{
                   String platform = item.getHeader("Platform");
                   if (StrUtil.contains(platform, "MOA")){
                       vo.setLinkType(NumberConstant.TWO);
                   } else {
                       vo.setLinkType(NumberConstant.ONE);
                   }
                });
        vo.setMkcIds(Collections.singletonList(req.getMkcId().toString()));
        R<List<BargainLinkVO>> result = productCloud.getBargainProductDetailLink(vo);
        log.warn("优品商详连接获取，传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(vo),JSONUtil.toJsonStr(result));
        if(!result.isSuccess()){
            throw new CustomizeException(Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
        }
        SuperiorProductsLinkRes superiorProductsLinkRes = new SuperiorProductsLinkRes();
        List<BargainLinkVO> data = result.getData();
        if(CollectionUtils.isNotEmpty(data)){
            BargainLinkVO bargainLinkVO = data.get(0);
            superiorProductsLinkRes.setProductDetailLink(bargainLinkVO.getProductDetailLink())
                    .setMkcId(bargainLinkVO.getMkcId());
        }
        return superiorProductsLinkRes;
    }

    @Override
    public List<ImeiQueryRes> imeiQuery(String imei, Integer limit, Boolean saveSearchLog) {
        if (StringUtils.isEmpty(imei)) {
            return null;
        }
        if (limit == null || limit == 0) {
            limit = 20;
        }
        //串号10位以上 按串号查询购买记录
        //if (imei.length() > 10) {
            if (ValidatorUtil.isPhone(imei)) {
                List<ImeiQueryRes> imeiQueryByMobile = baseMapper.getImeiQueryByMobile(new ImeiQueryReq(imei, limit, (int) Namespaces.get()));
                if(CollUtil.isNotEmpty(imeiQueryByMobile)){
                    return imeiQueryByMobile;
                }
            }
            //获取保修信息
            BaoxiuAndBuyBo wxServiceInfoBo = this.getServiceInfo(imei, saveSearchLog);
            if (wxServiceInfoBo != null && wxServiceInfoBo.getStats() == 1) {
                return Arrays.asList(bulidImeiQueryRes(wxServiceInfoBo));
            } else if (wxServiceInfoBo != null && wxServiceInfoBo.getIshuishou() != null && wxServiceInfoBo.getIshuishou() == 1) {
                List<HuishouInfoBo> huishouinfo = wxServiceInfoBo.getHuishouinfo();
                if (CollectionUtils.isNotEmpty(huishouinfo)) {
                    return huishouinfo.stream().map(e -> buildRecoverImeiQuery(e)).collect(Collectors.toList());
                }
            }
            //第三方API查询
            if (imei.length() == 15) {
                ImeiQueryInfoBo imeiQueryInfoBo = this.imeiQueryInfo(imei);
                if (imeiQueryInfoBo != null && CollectionUtils.isNotEmpty(imeiQueryInfoBo.getProductInfo())
                        && CollectionUtils.isNotEmpty(imeiQueryInfoBo.getProductInfo().get(NumberConstant.ZERO).getProductSpecInfo())) {
                    Integer productId = imeiQueryInfoBo.getProductInfo().get(NumberConstant.ZERO).getProductId();
                    String productName = imeiQueryInfoBo.getProductInfo().get(NumberConstant.ZERO).getProductName();
                    return imeiQueryInfoBo.getProductInfo().get(NumberConstant.ZERO).getProductSpecInfo().stream().map(e -> {
                        ImeiQueryRes imeiQueryRes = new ImeiQueryRes();
                        imeiQueryRes.setProductColor(e.getProductColor());
                        imeiQueryRes.setPpriceid(e.getPpid());
                        imeiQueryRes.setProductId(productId);
                        imeiQueryRes.setProductName(productName);
                        return imeiQueryRes;
                    }).collect(Collectors.toList());
                }
                return null;
            }
        //}
        return getImeiQuery(imei, limit, false, (int) Namespaces.get());
    }

    private ImeiQueryRes bulidImeiQueryRes(BaoxiuAndBuyBo baoxiuAndBuyBo) {
        ImeiQueryRes imeiQueryRes = new ImeiQueryRes();
        imeiQueryRes.setImei(baoxiuAndBuyBo.getImei());
        imeiQueryRes.setProductId(baoxiuAndBuyBo.getProductid());
        imeiQueryRes.setProductColor(baoxiuAndBuyBo.getProduct_color());
        imeiQueryRes.setMobile(baoxiuAndBuyBo.getSub_mobile());
        imeiQueryRes.setProductName(baoxiuAndBuyBo.getProduct_name());
        imeiQueryRes.setRealName(baoxiuAndBuyBo.getUsername());
        imeiQueryRes.setUserId(baoxiuAndBuyBo.getUserid());
        imeiQueryRes.setUserClass(baoxiuAndBuyBo.getUserclass());
        imeiQueryRes.setUserClassName(baoxiuAndBuyBo.getUserclassname());
        imeiQueryRes.setPpriceid(baoxiuAndBuyBo.getPpriceid());
        return imeiQueryRes;
    }

    private ImeiQueryRes buildRecoverImeiQuery(HuishouInfoBo huishouInfo) {
        ImeiQueryRes imeiQueryRes = new ImeiQueryRes();
        imeiQueryRes.setImei(huishouInfo.getImei());
        imeiQueryRes.setProductColor(huishouInfo.getProduct_color());
        imeiQueryRes.setMobile(huishouInfo.getSub_mobile());
        imeiQueryRes.setProductName(huishouInfo.getProduct_name());
        imeiQueryRes.setRealName(huishouInfo.getUsername());
        imeiQueryRes.setUserId(huishouInfo.getUserid());
        imeiQueryRes.setUserClass(huishouInfo.getUserclass());
        imeiQueryRes.setUserClassName(huishouInfo.getUserclassname());
        return imeiQueryRes;
    }

    private List<ImeiQueryRes> getImeiQuery(String imei, Integer top, Boolean isMobile, Integer xtenant) {
        //先通过手机号码或串号进行查询，如果查询不到再通过mkcId进行查询
        List<ImeiQueryRes> imeiQueryRes = baseMapper.getImeiQueryByMobileOrImei(imei, top, isMobile, xtenant);
        if (CollectionUtils.isNotEmpty(imeiQueryRes)) {
            return imeiQueryRes;
        }
        //通过库存id进行查询
        if (!isMobile) {
            List<Integer> mkcIds = productMkcService.getMkcIdByImei(imei);
            if (CollectionUtils.isEmpty(mkcIds)) {
                return new ArrayList<>();
            }
            List<ImeiQueryRes> imeiQueryRes1 = baseMapper.getImeiQueryByMkcIds(mkcIds, top, xtenant);
            if (CollectionUtils.isNotEmpty(imeiQueryRes1)) {
                return imeiQueryRes1;
            }
        } else {
            //上述查询结果都为空，再查询良品信息
            return CommenUtil.autoQueryHist(() -> baseMapper.getRecoverImeiQueryByMobile(imei, top, xtenant));
        }
        return null;
    }

    @Override
    public Integer getWxkcNumber(int ppid, int areaId) {
        if (ppid == 0 || areaId == 0) {
            return 0;
        }
        return baseMapper.getWxkcNumber(ppid, areaId);
    }


    /**
     * 根据ppid判断是否允许负库存出库
     * @param ppid
     * @return
     */
    private Boolean isNegativeInventory(Integer ppid){
        List<Integer> ppidList = productKcServiceImpl.getPpidList();
        List<Integer> cidList = productKcServiceImpl.getCidList();
        Productinfo productinfoByPpid = Optional.ofNullable(productinfoService.getProductinfoByPpid(ppid)).orElse(new Productinfo());
        Integer cid = productinfoByPpid.getCid();
        return ppidList.contains(ppid) || cidList.contains(cid);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<Boolean> lockWxPeijian(Integer yyId, Integer ppid, Integer areaId, Integer type) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        //锁定配件
        if (LockWxPeijianTypeEnum.LOCK.getCode().equals(type)) {
            ShouhouYuyue shouhouYuyue = Optional.ofNullable(shouhouYuyueService.lambdaQuery()
                    .eq(ShouhouYuyue::getId, yyId)
                    .apply("isnull(isdel,0)=0")
                    .one()).orElseThrow(()->new CustomizeException("预约单查询为空"));
            if(shouhouYuyueService.isUseNewYuYue()){
                String yuyuePPids = shouhouYuyue.getYuyuePPids();
                List<YuyuePpidsInfo> yuyuePpidsInfoList = new ArrayList<>();
                if (StringUtils.isNotEmpty(yuyuePPids)){
                    yuyuePpidsInfoList = JSON.parseArray(yuyuePPids, YuyuePpidsInfo.class);
                }
                Productinfo product = Optional.ofNullable(productinfoService.getProductinfoByPpid(ppid)).orElse(new Productinfo());
                String msg = String.format("先保存预约单配件：%s,再进行预留配件", product.getProductName());
                if(CollectionUtils.isNotEmpty(yuyuePpidsInfoList)){
                    List<Integer> yuYuePpIdList = yuyuePpidsInfoList.stream().map(YuyuePpidsInfo::getPpid).filter(Objects::nonNull).collect(Collectors.toList());
                    if(!yuYuePpIdList.contains(ppid)){
                        throw new CustomizeException(msg);
                    }
                } else {
                    throw new CustomizeException(msg);
                }

            }
            if( !Arrays.asList(YuyueStatusEnum.WQR.getCode(),YuyueStatusEnum.YWQR.getCode()).contains(shouhouYuyue.getStats())){
                return R.error(YuyueStatusEnum.WQR.getMessage()+","+YuyueStatusEnum.YWQR.getMessage()+"状态下的预约单才能能锁定配件");
            }
            if (areaId == null || areaId == 0) {
                return R.error("预约单地区错误");
            }
            if (yuyuelockppidsService.isLockWxPeijian(yyId, ppid)) {
                return R.error("已存在锁定记录，请勿重复操作");
            }
            Integer kcNum = productKcService.getKcCount(ppid, areaId);
            if (ObjectUtil.defaultIfNull(kcNum,0) < 1 && !isNegativeInventory(ppid)) {
                return R.error("库存余量不足，无法预留");
            }
            Boolean flag = productKcService.lockKc(ppid, areaId);
            if(!flag){
                throw new CustomizeException(String.format("库存不足, 锁定异常, 请核对库存量ppid: %s,areaId:%s",ppid,areaId));
            }
            yuyuelockppidsService.saveYuyueLockppids(yyId, ppid, areaId);
            yuyueLogsService.yuyueLogsAdd(yyId, "锁定配件【" + ppid + "】", oaUserBO.getUserName(), YuyueLogViewTypeEnum.WZ_NO_SHOUW.getCode());
            return R.success("库存已锁定1，预留成功", true);

        } else if (LockWxPeijianTypeEnum.NO_LOCK.getCode().equals(type)) {
            //解除锁定
            List<ShouhouYuyuelockppids> list = yuyuelockppidsService.lambdaQuery()
                    .eq(ShouhouYuyuelockppids::getYyid, yyId)
                    .list();
            List<ShouhouYuyuelockppids> currArealist = list.stream().filter(sylp -> ObjectUtil.equals(sylp.getAreaid(), areaId))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(currArealist)){
                currArealist.forEach(item ->{
                    productKcService.unlockKc(item.getPpid(), areaId);
                    yuyuelockppidsService.delYuyueLockppids(yyId, item.getPpid());
                });
                String unlockPpids = CommenUtil.joinInteger(currArealist.stream().map(ShouhouYuyuelockppids::getPpid).collect(Collectors.toList()), SignConstant.COMMA);
                yuyueLogsService.yuyueLogsAdd(yyId, "解除配件预留【" + unlockPpids + "】", oaUserBO.getUserName(), YuyueLogViewTypeEnum.WZ_NO_SHOUW.getCode());
            }else if(CollUtil.isNotEmpty(list)){
                return R.error("当前门店不是锁定门店，无法解除预留");
            }

            return R.success("解除预留成功", true);
        }
        return R.error("操作失败");
    }

    @Override
    public Boolean delYuyueUpdateShouhou(Integer yyid) {
        Integer num = baseMapper.delYuyueUpdateShouhou(yyid);
        if (num != null && num > 0) {
            return true;
        }
        return false;
    }

    @Override
    public String getOrderIdsh() {
        Integer week = LocalDateTime.now().getDayOfWeek().getValue();
        String[] Day = new String[]{"N", "H", "I", "J", "K", "L", "M"};
        String orderzm = Day[week - 1];
        List<String> list = baseMapper.getShouhouOrderIds(orderzm);
        Integer orderId =
                list.stream().map(e -> Integer.parseInt(e.substring(1))).sorted(Comparator.reverseOrder()).findFirst().orElse(0);
        orderId++;
        return orderzm + orderId;
    }

    @Override
    public Shouhou getShouhouInfoByImei(String imei) {
        return baseMapper.getShouhouInfoByImei(imei);
    }

    @Override
    public Boolean updateIsticheng(Integer id, Boolean isSoft) {
        UpdateWrapper<Shouhou> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(Shouhou::getIsticheng, 0).set(Shouhou::getIsfan, 1)
                .eq(Shouhou::getIssoft, isSoft).eq(Shouhou::getId, id);
        return this.update(updateWrapper);
    }

    /**
     * 处理配件信息
     * @param accessoryPpidList
     * @param shouHouId
     */
    private void handleAccessoryPpidList(List<AccessoryInfo> accessoryPpidList,Integer shouHouId){
        StringJoiner joiner = new StringJoiner(",");
        //查询允许负库存出库的ppid
        List<Integer> ppidList = productKcServiceImpl.getPpidList();
        List<Integer> cidList = productKcServiceImpl.getCidList();
        for (AccessoryInfo item: accessoryPpidList) {
            //如果过ppid 为0 那就不进行处理
            if(NumberConstant.ZERO.equals(item.getPpid())){
                continue;
            }
            Boolean isService = Optional.ofNullable(item.getIsService()).orElseThrow(()->new CustomizeException("前端传入accessoryPpidList服务参数为空 ppid:"+item.getPpid()));
            OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
            //判断商品如果是服务
            if(isService){
                Productinfo simpleKcRes = productinfoService.getProductinfoByPpid(item.getPpid());
                //维修单添加配件
                WxFeeBo wxFeeBo = new WxFeeBo();
                wxFeeBo.setKinds(2);
                wxFeeBo.setAreaId(oaUserBO.getAreaId());
                wxFeeBo.setPpid(item.getPpid());
              //  wxFeeBo.setInprice(item.getServicePrice());
                wxFeeBo.setPrice(item.getServicePrice());
                wxFeeBo.setPrice1(item.getServicePrice());
                wxFeeBo.setProductName(simpleKcRes.getProductName());
                wxFeeBo.setShouhouId(shouHouId);
                wxFeeBo.setUser("系统");
                R<ShouhouCostPriceRes>  result = shouhouService.addCostPrice(wxFeeBo, false, true);
                if(result.getCode()!=ResultCode.SUCCESS){
                    joiner.add("商品ppid"+item.getPpid()+Optional.ofNullable(result.getMsg()).orElse(result.getUserMsg()));
                }
            }
            //通过ppid查询获取配件信息
            List<ProductSimpleKcRes> productSimpleKcRes = productinfoService.searchProductKC(Optional.ofNullable(item.getPpid())
                    .orElse(Integer.MAX_VALUE).toString(), null);
            if(CollectionUtils.isNotEmpty(productSimpleKcRes) && !isService){
                ProductSimpleKcRes simpleKcRes = productSimpleKcRes.get(0);
                if(!ppidList.contains(simpleKcRes.getPpid()) && !cidList.contains(simpleKcRes.getCid()) && simpleKcRes.getLcount()<=0){
                    joiner.add("商品ppid"+simpleKcRes.getPpid()+"库存数量为0");
                    continue;
                }
                //维修单添加配件
                WxFeeBo wxFeeBo = new WxFeeBo();
                wxFeeBo.setKinds(2);
                wxFeeBo.setAreaId(oaUserBO.getAreaId());
                wxFeeBo.setPpid(simpleKcRes.getPpid());
                wxFeeBo.setInprice( simpleKcRes.getInprice());
                wxFeeBo.setPrice(simpleKcRes.getMemberPrice());
                wxFeeBo.setPrice1(simpleKcRes.getMemberPrice());
                wxFeeBo.setProductName(simpleKcRes.getProductName());
                wxFeeBo.setShouhouId(shouHouId);
                wxFeeBo.setUser("系统");
                R<ShouhouCostPriceRes>  result = shouhouService.addCostPrice(wxFeeBo, false, true);
                if(result.getCode()!=ResultCode.SUCCESS){
                    joiner.add("商品ppid"+simpleKcRes.getPpid()+Optional.ofNullable(result.getMsg()).orElse(result.getUserMsg()));
                }
            }
        }
        if(joiner.length()>NumberConstant.ZERO){
            String comment = joiner.toString();
            saveShouhouLog(shouHouId, comment, "系统", null, null);
        }

    }

    /**
     * 创建售后单
     * @param sh
     * @param oaUserBO
     * @return
     */
    @Override
    public R<Shouhou> createShouHou(ShouhouReq sh,OaUserBO oaUserBO){
        if (sh.getWxkind() != null && 5 == sh.getWxkind() && sh.getBaoxiu() != null && 2 != sh.getBaoxiu()) {
            sh.setBaoxiu(0);
        }
        if (sh.getSxsex() != null && 2 == sh.getSxsex()) {
            sh.setSxsex(null);
        }
        if (sh.getSxuserid() == null) {
            sh.setSxuserid(0);
        }
        Optional.ofNullable(sh.getRepairOrderId()).ifPresent(item->sh.setIsfan(Boolean.TRUE));
        // 完善商品信息
        enrichProductInfo(sh);
        String areaArr1[] = constantsSource.getWxerzuAreaids().split(",");
        String areaArr2[] = constantsSource.getWxyizuAreaids().split(",");
        if (Arrays.asList(areaArr1).contains(sh.getAreaid())) {
            sh.setWeixiuzuid(1);
        } else if (Arrays.asList(areaArr2).contains(sh.getAreaid())) {
            sh.setWeixiuzuid(14);
        }
        if (sh.getYuyueid() != null && sh.getYuyueid() > 1) {
            //预约处理组
            sh.setWeixiuzuid(25);
        }
        if (sh.getIssoft() == null) {
            sh.setIssoft(false);
        }
        if (sh.getIsquick() == null) {
            sh.setIsquick(false);
        }
        if (sh.getIsquji() == null) {
            sh.setIsquji(false);
        }
        if (sh.getIsfan() == null) {
            sh.setIsfan(false);
        }
        if (StringUtils.isNotEmpty(sh.getMobile())) {
            sh.setMobile(sh.getMobile().replaceAll(" ", ""));
            if (sh.getMobile().length() > 11) {
                throw new CustomizeException("手机号码长度有误");
            }
        }
        Shouhou shouhou = new Shouhou();
        BeanUtils.copyProperties(sh, shouhou);
        //判断如果过存在维修那单那就直接返回数据
        if(ObjectUtil.isNotNull(shouhou.getId())){
            return  R.success(shouhou);
        }
        // 未注册用户先注册成为会员
        Boolean userFlag = null == sh.getUserid() || 0 == sh.getUserid();
        // 有订单,优先从订单获取会员信息
        if(userFlag && ObjectUtil.defaultIfNull(sh.getSubId(), 0) >0){
            //没有会员信息
            Long userId = Optional.ofNullable(sh.getIshuishou()).filter(isHuiShou -> Objects.equals(isHuiShou, 1))
                    .map(isHuishou -> SpringUtil.getBean(SubService.class).getUserIdBySubId(sh.getSubId()))
                    .map(Convert::toLong)
                    .orElseGet(() -> Convert.toLong(SpringUtil.getBean(RecoverMarketinfoService.class).getUserIdBySubId(sh.getSubId())));
            if(ObjectUtil.defaultIfNull(userId, 0L) >0){
                sh.setUserid(userId);
                shouhou.setUserid(userId);
                userFlag = false;
            }
        }
        if (userFlag) {
            if (!CommenUtil.isMobile(shouhou.getMobile())) {
                return R.error("新用户自动注册失败，非法的手机号码!");
            }
            MemberReq memberReq = new MemberReq();
            memberReq.setXtenant((int) Namespaces.get());
            memberReq.setMobile(sh.getMobile());
            memberReq.setInUser(sh.getInuser());
            memberReq.setUserName(sh.getMobile());
            memberReq.setRealName(sh.getMobile());
            memberReq.setAreaId(Optional.ofNullable(sh.getAreaid()).filter(aid->aid>0).orElseGet(oaUserBO::getAreaId));
//            memberClient.checkAndRegisterUser(memberReq);
            R<Integer> result = memberClient.checkAndRegisterUser(memberReq);
            if (result.getCode() != ResultCode.SUCCESS) {
                return R.error(StrUtil.format("新用户注册失败,原因: {}！", result.getUserMsg()));
            }
            shouhou.setUserid(result.getData().longValue());
            shouhou.setUsername(shouhou.getMobile());
        }
        // 【九讯云】 分销政企用户售后流程限制加单功能开发
        CustomerAccountService customerAccountService = SpringUtil.getBean(CustomerAccountService.class);
        boolean isHuishou = Optional.ofNullable(shouhou.getIshuishou()).orElse(0) > 0;
        if(XtenantEnum.isSaasXtenant()
                && ObjectUtil.notEqual(CommonUtils.toShotWebXtenant(XtenantEnum.getXtenant()), ShortXtenantEnum.ZLF.getCode())
                && (ObjectUtil.defaultIfNull(shouhou.getUserid(),0L)>0
                && ObjectUtil.defaultIfNull(customerAccountService.getCustomerKinds(shouhou.getUserid()),0) >0
                // 原新机单为分销订单
                || !isHuishou && ObjectUtil.defaultIfNull(customerAccountService.getCustomerKinds(SpringUtil.getBean(SubService.class).getUserIdBySubId(shouhou.getSubId())),0)>0
                // 原良品单为分销订单
                || isHuishou && ObjectUtil.defaultIfNull(customerAccountService.getCustomerKinds(SpringUtil.getBean(RecoverMarketinfoService.class).getUserIdBySubId(shouhou.getSubId())),0)>0
        )
        ){
            throw new CustomizeException("分销、政企用户不允许添加维修单，如需退款请走“批量退款”功能进行操作。");
        }
        //处理ConnectionMethod 字段
        handleConnectionMethod(shouhou);
        Boolean flag = super.save(shouhou);
        if (!flag) {
            return R.error("生成售后单维修单失败");
        }
        //保存接件日志记录
        saveConnectionMethod(shouhou);
        CompletableFuture.runAsync(() -> {
            //跳转连接
            String host = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL))
                    .filter(r -> ResultCode.SUCCESS == r.getCode())
                    .map(R::getData)
                    .orElseThrow(() -> new RRException("获取域名出错"));
            String inuser = shouhou.getInuser();
            if (StringUtils.isNotEmpty(inuser)){
                List<TransferUserRes> transferUserRes = smallproWxMapper.selectUserNameList(Collections.singletonList(inuser));
                if (CollectionUtils.isNotEmpty(transferUserRes)){
                    Integer areaId = transferUserRes.get(0).getAreaId();
                    Areainfo areainfo = areainfoService.getById(areaId);
                    String message = "你关注的员工【" + inuser + "（" + areainfo.getArea() + "）】添加了维修单（单号：" + shouhou.getId() + "），请做好跟进~";
                    String link = host + "/new/#/afterService/order/detail/" + shouhou.getId();
                    this.sendSubCollectionMessage(transferUserRes.get(0).getCh999UserId(), link, message);
                }
            }
            String weixiuren = shouhou.getWeixiuren();
            if (StringUtils.isNotEmpty(weixiuren)){
                List<TransferUserRes> transferUserRes = smallproWxMapper.selectUserNameList(Collections.singletonList(weixiuren));
                if (CollectionUtils.isNotEmpty(transferUserRes)){
                    Integer areaId = transferUserRes.get(0).getAreaId();
                    Areainfo areainfo = areainfoService.getById(areaId);
                    String message = "你关注的员工【" + weixiuren + "（" + areainfo.getArea() + "）】添加了维修单（单号：" + shouhou.getId() + "），请做好跟进~";
                    String link = host + "/new/#/afterService/order/detail/" + shouhou.getId();
                    this.sendSubCollectionMessage(transferUserRes.get(0).getCh999UserId(), link, message);
                }
            }
        });
        return R.success(shouhou);
    }

    @Override
    public void sendSubCollectionMessage(Integer ch999Id,String link,String message){
        List<String> ch999IdList = this.baseMapper.searchSubCollect(ch999Id, 5);
        if(CollectionUtils.isNotEmpty(ch999IdList)){
            smsService.sendOaMsg(message, link,String.join(SignConstant.COMMA, ch999IdList), OaMesTypeEnum.MARKETING);
        }
    }
    /**
     * 处理ConnectionMethod 字段
     * @param shouhou
     */
    private void handleConnectionMethod(Shouhou shouhou){
        try {
            String connectionMethod = shouhou.getConnectionMethod();
            if(StringUtils.isNotEmpty(connectionMethod)){
                shouhou.setConnectionMethod("("+HttpClientUtil.getPlatformStr()+")"+connectionMethod);
            }
        } catch (Exception e){
            RRExceptionHandler.logError("处理ConnectionMethod字段异常",Dict.create().set("connectionMethod",shouhou.getConnectionMethod()).set("shouHouId",shouhou.getId()), e, smsService::sendOaMsgTo9JiMan);
        }
    }

    /**
     * 保存接件信息日志
     * @param shouhou
     */
    private void saveConnectionMethod(Shouhou shouhou){
        try {
            OaUserBO oaUserBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElse(new OaUserBO());
            String userName = Optional.ofNullable(oaUserBO.getUserName()).orElse("系统");
            String connectionMethod = shouhou.getConnectionMethod();
            // 接件方式日志记录
            StringBuilder log = new StringBuilder();
            if(StringUtils.isNotEmpty(connectionMethod)){
                String comment = "";
                if(HttpClientUtil.connectionMethodList().contains(connectionMethod)){
                    comment = String.format("添加维修单，接件方式：%s，预约单号：%s", connectionMethod,shouhouYuyueService.getYuYueIdUrl(shouhou.getYuyueid()));
                } else {
                    comment = String.format("添加维修单，接件方式：%s", connectionMethod);
                }
                log.append(comment);
            }
            String connectionFid = shouhou.getConnectionFid();
            if(StringUtils.isNotEmpty(connectionFid) && !Arrays.asList("undefined","null").contains(connectionFid.trim())){
                String fidUrl = imageProperties.getSelectImgUrl() + "newstatic/" + connectionFid;
                log.append(": <a target= _blank class=\"blue\" href=\"" + fidUrl + "\">" + shouhou.getImei() + "</a>");
            }
            if(log.length()>NumberConstant.ZERO){
                saveShouhouLog(shouhou.getId(),log.toString(), userName, null, true);
            }
        } catch (Exception e){
            RRExceptionHandler.logError("维修单记录接件来源异常",Dict.create().set("connectionMethod",shouhou.getConnectionMethod()).set("connectionFid",shouhou.getConnectionFid()).set("shouHouId",shouhou.getId()), e, smsService::sendOaMsgTo9JiMan);
        }
    }



    @Override
    public R<Boolean> saveShouhou(ShouhouReq sh) {
        //校验是否已存在售后单
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
/*//        Integer count = baseMapper.selectCount(new LambdaQueryWrapper<Shouhou>().eq(Shouhou::getImei, sh.getImei()).eq(Shouhou::getXianshi, 1).ne(Shouhou::getIsquji, 1));
//        if (count > 0) {
//            return R.error("该售后单已存在不能重复生成");
//        }

        //wxkind(维修处理方式): 1修 2换 3多 4送 5显示总成置换 6快
        //baoxiu(保修): 0不在保，1在保,2外修
        //置换单非外修机 锁定非保修
        if (sh.getWxkind() != null && 5 == sh.getWxkind() && sh.getBaoxiu() != null && 2 != sh.getBaoxiu()) {
            sh.setBaoxiu(0);
        }
        if (sh.getSxsex() != null && 2 == sh.getSxsex()) {
            sh.setSxsex(null);
        }
        if (sh.getSxuserid() == null) {
            sh.setSxuserid(0);
        }
        Optional.ofNullable(sh.getRepairOrderId()).ifPresent(item->sh.setIsfan(Boolean.TRUE));
        // 完善商品信息
        enrichProductInfo(sh);
        String areaArr1[] = constantsSource.getWxerzuAreaids().split(",");
        String areaArr2[] = constantsSource.getWxyizuAreaids().split(",");
        if (Arrays.asList(areaArr1).contains(sh.getAreaid())) {
            sh.setWeixiuzuid(1);
        } else if (Arrays.asList(areaArr2).contains(sh.getAreaid())) {
            sh.setWeixiuzuid(14);
        }
        if (sh.getYuyueid() != null && sh.getYuyueid() > 1) {
            //预约处理组
            sh.setWeixiuzuid(25);
        }
        if (sh.getIssoft() == null) {
            sh.setIssoft(false);
        }
        if (sh.getIsquick() == null) {
            sh.setIsquick(false);
        }
        if (sh.getIsquji() == null) {
            sh.setIsquji(false);
        }
        if (sh.getIsfan() == null) {
            sh.setIsfan(false);
        }
        if (StringUtils.isNotEmpty(sh.getMobile())) {
            sh.setMobile(sh.getMobile().replaceAll(" ", ""));
            if (sh.getMobile().length() > 11) {
                throw new CustomizeException("手机号码长度有误");
            }
        }
        Shouhou shouhou = new Shouhou();
        BeanUtils.copyProperties(sh, shouhou);
        // 未注册用户先注册成为会员
        Boolean userFlag = null == sh.getUserid() || 0 == sh.getUserid();
        // 有订单,优先从订单获取会员信息
        if(userFlag && ObjectUtil.defaultIfNull(sh.getSubId(), 0) >0){
            //没有会员信息
            Long userId = Optional.ofNullable(sh.getIshuishou()).filter(isHuiShou -> Objects.equals(isHuiShou, 1))
                    .map(isHuishou -> SpringUtil.getBean(SubService.class).getUserIdBySubId(sh.getSubId()))
                    .map(Convert::toLong)
                    .orElseGet(() -> Convert.toLong(SpringUtil.getBean(RecoverMarketinfoService.class).getUserIdBySubId(sh.getSubId())));
            if(ObjectUtil.defaultIfNull(userId, 0L) >0){
                sh.setUserid(userId);
                shouhou.setUserid(userId);
                userFlag = false;
            }
        }
        if (userFlag) {
            if (!CommenUtil.isMobile(shouhou.getMobile())) {
                return R.error("新用户自动注册失败，非法的手机号码!");
            }
            MemberReq memberReq = new MemberReq();
            memberReq.setXtenant((int) Namespaces.get());
            memberReq.setMobile(sh.getMobile());
            memberReq.setInUser(sh.getInuser());
            memberReq.setUserName(sh.getMobile());
            memberReq.setRealName(sh.getMobile());
            memberReq.setAreaId(Optional.ofNullable(sh.getAreaid()).filter(aid->aid>0).orElseGet(oaUserBO::getAreaId));
//            memberClient.checkAndRegisterUser(memberReq);
            R<Integer> result = memberClient.checkAndRegisterUser(memberReq);
            if (result.getCode() != ResultCode.SUCCESS) {
                return R.error(StrUtil.format("新用户注册失败,原因: {}！", result.getUserMsg()));
            }
            shouhou.setUserid(result.getData().longValue());
            shouhou.setUsername(shouhou.getMobile());
        }
        ShouhouReceiveReq shrr = null;
        String notBakDataDesc = null;
        //非软件接件且不需要备份资料的不需要备份资料进程不能为空
        if(!Boolean.TRUE.equals(sh.getIssoft()) && !NumberConstant.ONE.equals(sh.getIsBakData()) && sh instanceof ShouhouReceiveReq){
            shrr = (ShouhouReceiveReq) sh;
            if(StrUtil.isBlank(shrr.getNotBakDataDesc())){
                return R.error("不需要备份资料进程不能为空!");
            }else{
                notBakDataDesc = shrr.getNotBakDataDesc();
            }
        }
        // 【九讯云】 分销政企用户售后流程限制加单功能开发
        CustomerAccountService customerAccountService = SpringUtil.getBean(CustomerAccountService.class);
        boolean isHuishou = Optional.ofNullable(shouhou.getIshuishou()).orElse(0) > 0;
        if(XtenantEnum.isSaasXtenant()
                && ObjectUtil.notEqual(CommonUtils.toShotWebXtenant(XtenantEnum.getXtenant()), ShortXtenantEnum.ZLF.getCode())
                && (ObjectUtil.defaultIfNull(shouhou.getUserid(),0L)>0
                    && ObjectUtil.defaultIfNull(customerAccountService.getCustomerKinds(shouhou.getUserid()),0) >0
                    // 原新机单为分销订单
                    || !isHuishou && ObjectUtil.defaultIfNull(customerAccountService.getCustomerKinds(SpringUtil.getBean(SubService.class).getUserIdBySubId(shouhou.getSubId())),0)>0
                    // 原良品单为分销订单
                    || isHuishou && ObjectUtil.defaultIfNull(customerAccountService.getCustomerKinds(SpringUtil.getBean(RecoverMarketinfoService.class).getUserIdBySubId(shouhou.getSubId())),0)>0
                )
        ){
            throw new CustomizeException("分销、政企用户不允许添加维修单，如需退款请走“批量退款”功能进行操作。");
        }
        Boolean flag = super.save(shouhou);*/
        R<Shouhou> result = createShouHou(sh, oaUserBO);
        if (!result.isSuccess()) {
            return R.error(result.getUserMsg());
        }
        Shouhou shouhou = result.getData();
        sh.setId(shouhou.getId());
        Boolean isZdb = Boolean.FALSE;
        try {
            if(XtenantEnum.isJiujiXtenant()){
                String imei = shouhou.getImei();
                LocalDateTime now = LocalDateTime.now();
                List<Shouhou> shouhouList = this.lambdaQuery().eq(Shouhou::getImei, imei)
                        .between(Shouhou::getModidate, now.minusDays(90L), now)
                        .list();
                if(CollectionUtils.isNotEmpty(shouhouList) && shouhouList.size()>=NumberConstant.THREE){
                    StringJoiner joiner = new StringJoiner(",");
                    shouhouList.forEach(item->joiner.add(item.getId().toString()));
                    String comment="同一个串号90天内存在"+shouhouList.size()+"个维修单，单号"+joiner;
                    //重大办添加
                    R<Boolean> booleanR = SpringUtil.getBean(ShouhouImportantService.class).addShouhouImportantStats(sh.getId(), comment, 0);
                    if(booleanR.isSuccess()){
                        isZdb=Boolean.TRUE;
                        //维修单日志添加
                        saveShouhouLog(sh.getId(),comment, sh.getInuser(), 4, false);
                    }else {
                        throw new CustomizeException(Optional.ofNullable(booleanR.getUserMsg()).orElse(booleanR.getMsg()));
                    }
                }
            }
        }catch (Exception e){
            RRExceptionHandler.logError("加入重大办异常",shouhou.getId(),e,smsService::sendOaMsgTo9JiMan);
        }

        try {
            RepairOrderLogVO repairOrderLogVO = new RepairOrderLogVO();
            repairOrderLogVO.setInUser(shouhou.getInuser())
                    .setShouHouId(shouhou.getId())
                    .setOperationType(RepairOperationTypeEnum.ADD.getCode())
                    .setRepairOrderId(shouhou.getRepairOrderId());
            addRepairOrderLog(repairOrderLogVO);
        }catch (Exception e){
            RRExceptionHandler.logError("返修单日志记录异常",shouhou.getId(),e,smsService::sendOaMsgTo9JiMan);
        }


        //判断是九机并且是accessoryPpidList不为空的情况下才进行操作
        List<AccessoryInfo> accessoryPpidList = sh.getAccessoryPpidList();
        if(XtenantEnum.isJiujiXtenant() && CollectionUtils.isNotEmpty(accessoryPpidList)){
            handleAccessoryPpidList(accessoryPpidList,shouhou.getId());
        }
        //添加附件
        attachmentsService.saveAttachemnts(sh.getFiles(), sh.getId(), AttachmentsEnum.SHOUHOU.getCode(), null, sh.getModidate());
        //添加售后日志
        Boolean isweb = null != sh.getBiaozhi() && sh.getBiaozhi().equals("8");
        saveShouhouLog(sh.getId(), sh.getComment(), sh.getInuser(), null, isweb);
        //回收机库存维修日志
        String mkcLog = "售后维修单号：<a href=\"/shouhou/edit/" + sh.getId() + "\">" + sh.getId() + "</a>";
        saveRecoverMkcNewLogs(sh.getMkcId(), mkcLog, sh.getInuser(), false);
        //如果是售后机转入保存，则记录为退换亏损
        BigDecimal memberPrice = null;
        Productinfo productinfo = productinfoService.getProductinfoByPpid(sh.getPpriceid());
        if (null != productinfo) {
            memberPrice = productinfo.getMemberprice();
        }
        ShouhouReturncb shouhouReturncb = new ShouhouReturncb();
        shouhouReturncb.setShouhouId(sh.getId());
        shouhouReturncb.setOldshouhouId(sh.getId());
        shouhouReturncb.setShzrPrice(memberPrice);
        shouhouReturncb.setPpid(sh.getPpriceid());
        shouhouReturncb.setTuihuanId(0);
        shouhouReturncb.setTuihuanKind(5);
        shouhouReturncb.setDtime(LocalDateTime.now());
        shouhouReturncb.setMkcId(sh.getMkcId());
        shouhouReturncbService.saveShouhouReturncb(shouhouReturncb);
        //更新用户行为推送
        if (null != sh.getUserid() && 0 != sh.getUserid()) {
            memberClient.updateNoticeWay(sh.getUserid().intValue(), ENoticeWayEnum.SHOU_HOU_RECYCLE.getCode(), shouhou.getId().toString());
        }
        //记录接件时间
        shouhouTimePointService.saveShouhouTimePoint(sh.getId(), ShouhouTimePointTypeEnum.JJ.getCode(), sh.getModidate());
        //消息推送
        Boolean isSendWxMsg = true;
        //有服务码不推送
        if (null != sh.getYuyueid() && sh.getYuyueid() != 0) {
            ShouhouYuyue shouhouYuyue = shouhouYuyueService.getById(sh.getYuyueid());
            if (shouhouYuyue != null && StringUtils.isNotEmpty(shouhouYuyue.getFuwuma())) {
                isSendWxMsg = false;
            }
        }
        if ((null != sh.getIssoft() && !sh.getIssoft() || XtenantEnum.isJiujiXtenant()) && sh.getUserid() != null && sh.getUserid() != 0 && !sh.getUserid().equals(76783L) && isSendWxMsg) {
            ShouhouMsgPushMessageBo shouhouMsgPushMessageBo = new ShouhouMsgPushMessageBo();
            shouhouMsgPushMessageBo.setMsgId(1);
            shouhouMsgPushMessageBo.setShouhouId(sh.getId());
            shouhouMsgPushMessageBo.setAreaId(sh.getAreaid());
            shouhouMsgPushMessageBo.setUserId(sh.getUserid().intValue());
            shouhouMsgPushMessageBo.setOptUser(oaUserBO.getUserName());
            //提交生成软件单,故障类别为“客户自送厂家售后（ppid：426818）”推送进度文案调整为：引导您自行送至厂家售后处理
            if (XtenantEnum.isJiujiXtenant() && Boolean.TRUE.equals(sh.getIssoft()) && sh instanceof ShouhouReceiveReq) {
                ShouhouReceiveReq shouhouReceiveReq = (ShouhouReceiveReq) sh;
                if (Objects.equals(426818, shouhouReceiveReq.getSoftFaultPpid())) {
                    shouhouMsgPushMessageBo.setMsgId(44);
                }
            }

            //钻石会员消息推送
//            smsService.sendDiamondMemberMsgWhenBusiness(Math.toIntExact(sh.getUserid()),sh.getAreaid(), BusinessTypeEnum.AFTER_ORDER.getCode(),sh.getId());
            CompletableFuture.runAsync(() -> smsService.sendDiamondMemberMsgWhenBusiness(Math.toIntExact(sh.getUserid()), sh.getAreaid(), BusinessTypeEnum.AFTER_ORDER.getCode(), sh.getId()));
            this.pushMessage(shouhouMsgPushMessageBo, true);
        } else {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(sh.getAreaid());
            AreaInfo areaInfo = areaInfoR.getData();
            if (ResultCode.SUCCESS == areaInfoR.getCode() && areaInfo != null) {
                String value = areaInfo.getPrintName();
                if (StrUtil.isBlank(value) && oaUserBO != null) {
                    value = sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.PRINT_NAME, oaUserBO.getXTenant());
                }
                //保存接件进程
                saveShouhouLog(sh.getId(), "您的设备已录入，感谢您选择" + value, sh.getInuser(), null, true);
            }
        }
        // 只有九机使用
        //  并非主线业务所以异步进行日志进程处理
        //  如果维修单客户是钻级会员，则维修单生成成功时，新增日志进程【尊敬的九机钻级会员您好，九机向高级会员提供尊享免费高价值备用机服务，感谢您对九机信任与支持。 】，该进程用户可视
        if(XtenantEnum.Xtenant_JIUJI.getCode().equals(XtenantEnum.getXtenant())){
            CompletableFuture.runAsync(()->{
                R<MemberBasicRes> memberBasicInfoR = memberClient.getMemberBasicInfo(sh.getUserid().intValue());
                MemberBasicRes memberBasicRes = com.jiuji.tc.utils.common.CommonUtils.isRequestSuccess(memberBasicInfoR) ? memberBasicInfoR.getData() : null;
                if (memberBasicRes != null) {
                    boolean isDiamondMember = UserClassEnum.USER_CLASS_DIAMOND.getCode().equals(memberBasicRes.getUserClass())
                            || UserClassEnum.USER_CLASS_DOUBLE_DIAMOND.getCode().equals(memberBasicRes.getUserClass());
                    if(isDiamondMember){
                        saveShouhouLog(sh.getId(),"尊敬的九机钻级会员您好，九机向高级会员提供尊享免费高价值备用机服务，感谢您对九机信任与支持。", sh.getInuser(), null, true);
                    }
                }
            });
        }
        ShouhouReceiveReq shrr = null;
        String notBakDataDesc = null;
        //非软件接件且不需要备份资料的不需要备份资料进程不能为空
        if(!Boolean.TRUE.equals(sh.getIssoft()) && !NumberConstant.ONE.equals(sh.getIsBakData()) && sh instanceof ShouhouReceiveReq){
            shrr = (ShouhouReceiveReq) sh;
            if(StrUtil.isBlank(shrr.getNotBakDataDesc())){
                return R.error("不需要备份资料进程不能为空!");
            }else{
                notBakDataDesc = shrr.getNotBakDataDesc();
            }
        }
        //不需要备份资料,添加进程,并推送微信消息 工单地址: https://pm.9ji.com/index.php?m=task&f=view&taskID=49607
        if(StrUtil.isNotBlank(notBakDataDesc)){
            //保存接件进程
            saveShouhouLog(sh.getId(),notBakDataDesc, sh.getInuser(), null, true);
            shouhouMsgService.sendWeixinMsg(sh.getId(),notBakDataDesc,false,false);
        }
        shouhouOtherService.recordProgress(shouhou.getId(), sh.getWeixiuren(), sh.getInuser());
        //三方订单状态同步
        thirdPlatStatusSyncService.thirdShouhouStatusSync(ShouhouOrderTypeEnum.SHOUHOU.getCode(), shouhou.getId());
        R<Boolean> success = R.success("保存成功", true);
        Map<String, Object> map = new HashMap<>();
        map.put(IS_ZDB,isZdb);
        success.setExData(map);
        return success;
    }
    /**
     * 关联返修单日志记录
     * @param repairOrderLogVO
     */
    @Override
    public void addRepairOrderLog(RepairOrderLogVO repairOrderLogVO){
        Integer repairOrderId = repairOrderLogVO.getRepairOrderId();
        Integer operationType = repairOrderLogVO.getOperationType();
        if(RepairOperationTypeEnum.ADD.getCode().equals(operationType) && ObjectUtil.isNotNull(repairOrderId)){
            //新增的逻辑
            String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.OA_URL)).map(R::getData).filter(org.apache.commons.lang3.StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
            String url = String.format("<a href=\"%s/staticpc/#/after-service/order/edit/%s\">%s</a>", host, repairOrderId,repairOrderId);
            String comment = String.format("维修单被标记为返修维修单，关联原维修单号:%s", url);
            saveShouhouLog(repairOrderLogVO.getShouHouId(), comment,  repairOrderLogVO.getInUser(), null, false);
        }else if(RepairOperationTypeEnum.DELETE.getCode().equals(operationType)){
            //删除的逻辑
            String comment="取消标记为“返修维修单”";
            saveShouhouLog(repairOrderLogVO.getShouHouId(), comment,  repairOrderLogVO.getInUser(), null, false);
        }
    }

    /**
     * 晚上售后单提交的商品信息
     * @param sh
     */
    private void enrichProductInfo(ShouhouReq sh) {
        if(ObjectUtil.defaultIfNull(sh.getPpriceid(),0) == 0
                && ObjectUtil.defaultIfNull(sh.getSubId(),0)>0
                && ObjectUtil.defaultIfNull(sh.getBasketId(),0)>0){
            //补充ppid
            sh.setPpriceid(SpringUtil.getBean(ShouhouExtendService.class).getPpriceIdByShouhouInfo(sh));
        }else if(ObjectUtil.defaultIfNull(sh.getPpriceid(),0) <= 0){
            //通过名称来进行补偿ppid 和productId
            productinfoService.lambdaQuery().eq(Productinfo::getProductColor, sh.getProductColor())
                    .eq(Productinfo::getProductName, sh.getName()).list().stream()
                    .findFirst()
                    .ifPresent(pi -> {
                        sh.setPpriceid(pi.getPpriceid());
                        if(CommenUtil.isNullOrZero(sh.getProductId())){
                            sh.setProductId(Convert.toLong(pi.getProductId()));
                        }
                    });
        }
        //补充productId
        if(Objects.nonNull(sh.getPpriceid()) && CommenUtil.isNullOrZero(sh.getProductId())){
            // 通过ppid来补充productId
            sh.setProductId(Optional.ofNullable(productinfoService.getProductinfoByPpid(sh.getPpriceid()))
                    .map(Productinfo::getProductId).map(Convert::toLong).orElse(null));
        }else if(StrUtil.isNotBlank(sh.getName()) && CommenUtil.isNullOrZero(sh.getProductId())){
            // 通过商品名称来补充productId
            sh.setProductId(productinfoService.getProductIdByProductName(sh.getName()));
        }
    }

    @Override
    public PageVo getShouhouPage(ShouhouListReq param) {
        PageVo result = new PageVo(param.getCurrent(), param.getSize());
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return result;
        }
        Integer total = baseMapper.getShouhouTotal(param);
        if (total == 0) {
            return result;
        }
        param.setStartRow((param.getCurrent() - 1) * param.getSize());
        List<ShouhouListBo> list = baseMapper.getShouhouList(param);
        result.setTotal(total);
        Map<Integer, String> realNameMap = null;
        if (AuthorizeIdEnum.CHINA_POST_NO_AIYIN_AREA.getCode().equals(oaUserBO.getAuthorizeId())) {
            List<Integer> userIds = list.stream().map(e -> e.getHuishouUserId()).distinct().collect(Collectors.toList());
            realNameMap = zhongyouApplyService.getCompanyByUserId(userIds);
        }
        Map<Integer, String> map = realNameMap;
        List<Integer> shouhouIds = list.stream().map(e -> e.getShouhouId()).collect(Collectors.toList());
        List<ShouhouTestResultBo> testResultBoList = shouhoutestInfoService.checkShouhouTestResult(shouhouIds);
        List<Integer> peijianfahuoList = caigouBasketRefShouhouService.getPeijianfahuo(shouhouIds);
        List<ShouhouListRes> records = list.stream().map(e -> {
            ShouhouListRes shouhouListRes = new ShouhouListRes();
            BeanUtils.copyProperties(e, shouhouListRes);
            if (map != null && map.containsKey(e.getHuishouUserId())) {
                shouhouListRes.setRealName(map.get(e.getHuishouUserId()));
            }
            Long testCount = testResultBoList.stream().filter(e1 -> e1.getShouhouId().equals(e.getShouhouId())).count();
            if (testCount > 0) {
                shouhouListRes.setTestResult(true);
            } else {
                shouhouListRes.setTestResult(false);
            }
            if (peijianfahuoList.contains(e.getShouhouId())) {
                shouhouListRes.setIsPeijianFahuo(true);
            } else {
                shouhouListRes.setIsPeijianFahuo(false);
            }
            shouhouListRes.setStatusName(EnumUtil.getMessageByCode(WxStatusEnum.class, e.getStatus()));
            shouhouListRes.setBaoxiuName(EnumUtil.getMessageByCode(BaoxiuStatusEnum.class, e.getStatus()));
            return shouhouListRes;
        }).collect(Collectors.toList());
        result.setRecords(records);
        return result;
    }

    @Override
    public LocalDateTime getModidate(Integer shouhouId) {
        List<Shouhou> shouhouList = baseMapper.selectList(new LambdaQueryWrapper<Shouhou>().eq(Shouhou::getId, shouhouId).select(Shouhou::getModidate));
        if (CollectionUtils.isNotEmpty(shouhouList)) {
            return shouhouList.get(0).getModidate();
        }
        return null;
    }

    @Override
    public BaoxiuAndBuyBo getServiceInfo(String imei, Boolean saveSearch) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return null;
        }

        ServiceInfoVO serviceInfo = serviceRecordService.getRecord(imei, null, saveSearch);
        return BaoxiuAndBuyBoWrapper.byServiceInfo(serviceInfo);
    }

    @Override
    public R<String> getServiceInfoV2(String imei, Boolean saveSearch) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return null;
        }
        ServiceInfoVO serviceInfo = serviceRecordService.getRecord(imei, null, saveSearch);
        JSONObject result = JSON.parseObject(JSON.toJSONStringWithDateFormat(BaoxiuAndBuyBoWrapper.getServiceInfoMap(serviceInfo),TimeFormatConstant.YYYY_MM_DD_HH_MM_SS, SerializerFeature.WriteMapNullValue));

        result.put("imeilist",Convert.toList(ImeiSearchLogBo.class,serviceInfo.getImeilist()).stream()
                .map(imeiLog-> Dict.create().set("ch999_user",imeiLog.getUserName())
                            .set("dtime",imeiLog.getDTime().format(DateTimeFormatter.ofPattern(TimeFormatConstant.YYYY_MM_DD_HH_MM)))
                            .set("isclear",imeiLog.getClear()).set("area",imeiLog.getArea()).set("ch999_id",imeiLog.getCh999Id()))
                .collect(Collectors.toList()));

        return R.success("操作成功", JSON.toJSONString(result));
    }

    @Override
    public ImeiQueryInfoBo imeiQueryInfo(String imei) {

        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null || oaUserBO.getXTenant() == null) {
            return null;
        }
        return getImeiQueryInfoNotLimitUser(imei);
    }

    @Override
    public ImeiQueryInfoBo getImeiQueryInfoNotLimitUser(String imei) {
        if (StringUtils.isEmpty(imei)) {
            return null;
        }
        String url = StrUtil.format("https://mdm.9ji.com/api/imei/queryInfoV2?imei={}", imei);
        String ret = HttpClientUtil.get(url);
        if (StringUtils.isNotEmpty(ret)) {
            R<ImeiQueryInfoBo> infoBoR = JSON.parseObject(ret, new TypeReference<R<ImeiQueryInfoBo>>() {
            });
            Optional.ofNullable(infoBoR.getData()).map(ImeiQueryInfoBo::getProductInfo)
                    .map(pis -> pis.stream().filter(pi -> CollUtil.isNotEmpty(pi.getProductSpecInfo()))
                            .flatMap(psis -> psis.getProductSpecInfo().stream()))
                    //去掉商品规格的前后空格
                    .ifPresent(psi -> psi.forEach(ps ->{
                        ps.setProductColor(StrUtil.trim(ps.getProductColor()));
                    }))

            ;
            return infoBoR.getData();
        }
        return null;
    }

    /**
     * 获取配件信息
     * @param shouhou
     * @return
     */
    private List<LockWxpjBo> createWxpjList(Shouhou shouhou,ShouhouInfoRes shouhouInfoRes){
        List<LockWxpjBo> wxpjBoList = new ArrayList<>();
        Integer yuyueid = shouhou.getYuyueid();
        //如果过没有预约id那就直接进行数据返回空数组
        if(ObjectUtil.isNull(yuyueid)){
            return wxpjBoList;
        }
        ShouhouYuyueService yuyueService = SpringUtil.getBean(ShouhouYuyueService.class);
        ShouhouYuyue yuyue = yuyueService.getById(yuyueid);
        //如果过预约单查询为空直接返回空数组
        if(ObjectUtil.isNull(yuyue)){
            return wxpjBoList;
        }
        shouhouInfoRes.setYuYueFromSource(yuyue.getFromSource());
        String wxpjJson = yuyue.getYuyuePPids();
        //如果过预约单查询为空直接返回空数组
        if(ObjectUtil.isNull(wxpjJson)){
            return wxpjBoList;
        }
        try {
            List<Integer> wxpjPpids = new LinkedList<>();
            List<YuyuePpidsInfo> wxpjList;
            List<YuyuePpidsInfo> wxpjPpidIsNull = null;
            Map<Integer, YuyuePpidsInfo> wxpjMap = null;
            if (CommenUtil.isNumer(wxpjJson)) {
                wxpjPpids.add(Integer.valueOf(wxpjJson));
            } else {
                wxpjList = JSON.parseArray(wxpjJson, YuyuePpidsInfo.class);
                if (CollectionUtils.isNotEmpty(wxpjList)) {
                    wxpjPpids = wxpjList.stream().map(e -> e.getPpid()).collect(Collectors.toList());
                    wxpjMap = wxpjList.stream().collect(Collectors.toMap(e -> e.getPpid(), e -> e, (v1, v2) -> v1));
                    wxpjPpidIsNull = wxpjList.stream().filter(e -> e.getPpid() == 0 || e.getPpid() == null).collect(Collectors.toList());
                }
            }
            wxpjBoList = yuyueService.getLockWxpj(yuyueid, wxpjPpids);
            //ppid为0
            if (wxpjPpidIsNull != null && !wxpjPpidIsNull.isEmpty()) {
                LockWxpjBo lockWxpjBo = new LockWxpjBo();
                BeanUtils.copyProperties(wxpjPpidIsNull.get(0), lockWxpjBo);
                lockWxpjBo.setProductName(lockWxpjBo.getFaultDes());
                wxpjBoList.add(lockWxpjBo);
            }
            if (CollectionUtils.isNotEmpty(wxpjBoList)) {
                for (LockWxpjBo wxpjBo : wxpjBoList) {
                    if (wxpjMap != null && wxpjMap.containsKey(wxpjBo.getPpid())) {
                        YuyuePpidsInfo yuyuePpidsInfo = wxpjMap.get(wxpjBo.getPpid());
                        BeanUtils.copyProperties(yuyuePpidsInfo, wxpjBo);
                        wxpjBo.setFaultDes(yuyuePpidsInfo.getTroubleDes());
                        if (wxpjBo.getPpid() == 0) {
                            //前端取值不方便，让后端处理一下
                            wxpjBo.setProductName(yuyuePpidsInfo.getTroubleDes());
                        }
                    }
                }

            }
        } catch (Exception e) {
            String format = String.format("售后单查询预约单配件列表异常，维修单id：%s,json化字符串：%s" + shouhou.getId(),wxpjJson);
            SpringUtil.getBean(SmsService.class).sendOaMsgTo9Ji(format, "13495",OaMesTypeEnum.YCTZ.getCode().toString());
            log.error(format,e);
        }
        return wxpjBoList;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> updatePrice(UpdatePriceVO updatePrice) {
        Integer shouhouId = updatePrice.getShouhouId();
        Shouhou shouhou = Optional.ofNullable(this.getById(shouhouId)).orElseThrow(() -> new CustomizeException("维修单查询为空"));
        //判断订单为已修好的状态
        if(!WxStatusEnum.YXH.getCode().equals(shouhou.getStats())){
             throw new CustomizeException("维修单不是已修好的状态不能修改价格");
        }
        //判断是否为员工
        Ch999UserBasicBO ch999UserBasicBO = getCh999UserBasicBO(shouhou.getMobile());
        if(ObjectUtil.isNull(ch999UserBasicBO)){
            throw new CustomizeException("该订单送修号码并非员工，不可以改价");
        }
        //判断提交人是否为直属上级
        List<Ch999UserBasicBO> ch999UserBasicBOS = shouhouExMapper.selectDirectSuperiorLeaders(ch999UserBasicBO.getCh999Id());
        if(CollectionUtils.isEmpty(ch999UserBasicBOS)){
            throw new CustomizeException("当前人没有直属上级");
        }
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录失效请重新登录"));
        if(!ch999UserBasicBOS.stream().map(Ch999UserBasicBO::getCh999Id).collect(Collectors.toSet()).contains(userBO.getUserId())){
            throw new CustomizeException("当前人不是该订单的直属上级");
        }
        //查询是否使用优惠码
        if (shouhou.getYouhuifeiyong() != null && shouhou.getYouhuifeiyong().compareTo(BigDecimal.ZERO) > 0) {
            throw new CustomizeException("此单已使用优惠码，不允许改价！");
        }
        //修改wxkcoutput表里面的price=inprice+手工费 然后系统会自动核算
        // 维修配件信息
        List<Wxkcoutput> wxkcoutputs = wxkcoutputService.lambdaQuery().eq(Wxkcoutput::getWxid, shouhouId)
                .and(cnd -> cnd.ne(Wxkcoutput::getPrice1, 0).or().isNull(Wxkcoutput::getPpriceid).or().eq(Wxkcoutput::getPpriceid, 0))
                .and(cnd -> cnd.ne(Wxkcoutput::getStats, Wxkcoutput.StatusEnum.CANCELED.getCode())
                        .or().eq(Wxkcoutput::getTuiStatus, Wxkcoutput.TuiStatusEnum.REFUND_ONLY.getCode())).list();
        if(CollectionUtils.isEmpty(wxkcoutputs)){
            throw new CustomizeException("该订单没有维修配件信息,无需改价");
        }
        Map<Integer, Wxkcoutput> wxkcoutputMap = wxkcoutputs.stream().collect(Collectors.toMap(Wxkcoutput::getId, Function.identity()));
        //获取改价信息
        List<AccessoryUpdatePriceVo> updatePriceList = getUpdatePriceList(wxkcoutputs);
        StringJoiner comment = new StringJoiner(",");
        if(CollectionUtils.isNotEmpty(updatePriceList)){
            updatePriceList.forEach(item->{
                //成本价格+手工费
                BigDecimal price = Optional.ofNullable(item.getInPrice()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(item.getServiceFee()).orElse(BigDecimal.ZERO));
                boolean update = wxkcoutputService.lambdaUpdate()
                        .eq(Wxkcoutput::getId, item.getWxkcoutputId())
                        .set(Wxkcoutput::getPrice, price)
                        .update();
                if(!update){
                    throw new CustomizeException("维修配件id:"+item.getWxkcoutputId()+"价格修改失败");
                } else {
                    //改价日志记录
                    Optional.ofNullable(wxkcoutputMap.get(item.getWxkcoutputId())).ifPresent(wxkcoutput->{
                        comment.add(wxkcoutput.getName()+"修改由"+wxkcoutput.getPrice()+"修改为"+price);
                    });
                }
            });
        }
        //维修费用的修改
        R<Boolean> booleanR = repairService.updateShouhouFeiyong(shouhouId);
        if(!booleanR.getData()){
            throw new CustomizeException(Optional.ofNullable(booleanR.getMsg()).orElse(booleanR.getMsg()));
        }
        //改价记录
        EmployeeOrdersService employeeOrdersService = SpringUtil.getBean(EmployeeOrdersService.class);
        Long userId = Optional.ofNullable(shouhou.getUserid()).orElseThrow(() -> new CustomizeException("维修单userid为空"));
        //根据会员id查询员工信息
        List<Ch999UserBasicBO> basicBOS = shouhouExMapper.selectUserById(userId);
        if(CollectionUtils.isEmpty(basicBOS)){
            throw new CustomizeException("根据会员id查询员工信息为空");
        }
        Ch999UserBasicBO basicBO = basicBOS.get(NumberConstant.ZERO);
        EmployeeOrders employeeOrders = new EmployeeOrders();
        employeeOrders.setSubId(shouhouId)
                .setKind(EmployeeOrdersKindEnum.AFTER_SALES.getCode())
                .setCh999Id(basicBO.getCh999Id())
                .setMobile(Optional.ofNullable(basicBO.getMobile()).orElseThrow(()->new CustomizeException(basicBO.getCh999Name()+"手机号码为空")))
                .setIsDel(Boolean.FALSE)
                .setCreateTime(LocalDateTime.now())
                .setUpdateTime(LocalDateTime.now());
        boolean save = employeeOrdersService.save(employeeOrders);
        if(!save){
            throw new CustomizeException("改价记录失败");
        }
        //写维修单日志
        shouhouService.saveShouhouLog(shouhouId, updatePrice.getRemark(), userBO.getUserName(), null, true);
        //改价日志记录
        if(StringUtils.isNotEmpty(comment.toString())){
            shouhouService.saveShouhouLog(shouhouId, comment.toString(), userBO.getUserName(), null, true);
        }
        return booleanR ;
    }

    /**
     * 获取维修配件对应的改价之后的价格
     * @return
     */
    private List<AccessoryUpdatePriceVo> getUpdatePriceList(List<Wxkcoutput> wxkcoutputs) {
        List<AccessoryUpdatePriceVo> list = new ArrayList<>();
        //收集wxkcoutputs里面的ppid
        Set<Integer> ppidSet = wxkcoutputs.stream().map(Wxkcoutput::getPpriceid).collect(Collectors.toSet());
        Map<Integer, ServiceFeeBO> serviceFeeBOMap = feeConfigService.selectServiceFeeByPpidList(ppidSet);
        wxkcoutputs.forEach(item -> {
            ServiceFeeBO feeBo = serviceFeeBOMap.get(item.getPpriceid());
            if(ObjectUtil.isNotNull(feeBo)){
                AccessoryUpdatePriceVo accessoryUpdatePriceVo = new AccessoryUpdatePriceVo();
                accessoryUpdatePriceVo.setPpId(item.getPpriceid())
                        .setWxkcoutputId(item.getId())
                        .setServiceFee(feeBo.getServiceFee())
                        .setCid(feeBo.getCid())
                        .setInPrice(item.getInprice());
                list.add(accessoryUpdatePriceVo);
            }
        });
        return list;
    }

    /**
     *
     * @param mobile
     * @return
     */
    private Ch999UserBasicBO getCh999UserBasicBO(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return null;
        }
        List<Ch999UserBasicBO> ch999UserBasicBOS = shouhouExMapper.selectUserByMobile(mobile);
        if(CollectionUtils.isNotEmpty(ch999UserBasicBOS)){
            return ch999UserBasicBOS.get(NumberConstant.ZERO);
        }
        return null;
    }
    /**
     * 创建维修单改价
     * @param shouhou
     * @param shouhouInfoRes
     */
    private void createUpdatePriceInfo(Shouhou shouhou, ShouhouInfoRes shouhouInfoRes) {
        try {
            if (XtenantEnum.isJiujiXtenant()) {
                UpdatePriceInfoRes updatePriceInfoRes = new UpdatePriceInfoRes();
                //获取手机号(如果维修单的送修号码那就取值会员号码)
                Optional.ofNullable(getCh999UserBasicBO(shouhou.getMobile())).ifPresent(ch999UserBasicBO -> {
                    //查询使用过的该号码使用userId
                    List<Long> userIdList = SpringUtil.getBean(BbsxpUsersService.class)
                            .lambdaQuery().eq(BbsxpUsers::getMobile, shouhou.getMobile())
                            .eq(BbsxpUsers::getXtenant, XtenantEnum.getXtenant())
                            .list().stream().map(item -> Convert.toLong(item.getId()))
                            .collect(Collectors.toList());
                    //判断订单为已修好的状态
                    if (WxStatusEnum.YXH.getCode().equals(shouhou.getStats())) {
                        shouhouInfoRes.setDisplayPriceChange(Boolean.TRUE);
                    }
                    updatePriceInfoRes.setUserName(ch999UserBasicBO.getCh999Name());
                    // 获取当前日期对应的年份
                    LocalDate currentYearFirstDay = LocalDate.now(ZoneId.systemDefault()).withDayOfYear(1);
                    // 创建一个表示今年开始时间的 LocalDateTime 对象
                    LocalDateTime startOfYear = currentYearFirstDay.atStartOfDay();
                    List<Integer> list = this.lambdaQuery()
                            .and(item -> item.eq(Shouhou::getMobile, shouhou.getMobile()).or().in(CollectionUtils.isNotEmpty(userIdList),Shouhou::getUserid, userIdList))
                            .and(item -> item.gt(Shouhou::getFeiyong, BigDecimal.ZERO).or().gt(Shouhou::getCostprice, BigDecimal.ZERO))
                            .eq(Shouhou::getIsquji, Boolean.TRUE)
                            .between(Shouhou::getOfftime, startOfYear, startOfYear.plusYears(NumberConstant.ONE))
                            .select(Shouhou::getId)
                            .list().stream().map(Shouhou::getId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(list)) {
                        updatePriceInfoRes.setShouHouIdList(list);
                        updatePriceInfoRes.setMaintenanceCount(list.size());
                    }
                    shouhouInfoRes.setUpdatePriceInfo(updatePriceInfoRes);
                });
            }
        } catch (Exception e) {
            RRExceptionHandler.logError(StrUtil.format("维修单[{}]查询员工改价异常", shouhou.getId()), shouhou, e, smsService::sendOaMsgTo9JiMan);
        }
    }
    @Override
    public R<ShouhouInfoRes> getShouhouInfo(Integer shouhouId) {
        Long begin = 0L;
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Shouhou shouhou = baseMapper.selectById(shouhouId);
        if (shouhou == null) {
            return R.error("售后维修信息不存在");
        }
        shouhou.setIssoft(ObjectUtil.defaultIfNull(shouhou.getIssoft(),Boolean.FALSE));
        Boolean authPart = authConfigService.isAuthPart(oaUserBO);
        if (authPart) {
            //当前登录地区和小件单所属地区不在同一个授权体系下不能进行查看
            Areainfo areaInfo = areainfoService.getById(CommenUtil.currId(shouhou.getToareaid(),shouhou.getAreaid()));
            if (!Objects.equals(oaUserBO.getAuthorizeId(), areaInfo.getAuthorizeid())
                    && !Objects.equals(shouhou.getAreaid(), oaUserBO.getAreaId())) {
                return R.error("未授权的访问操作");
            }
        }

        CompletableFuture future1 = CompletableFuture.runAsync(() -> shouHouPjService.insertSearchHistory(oaUserBO.getUserId(), shouhouId + "|2", "oaSearch"), this.pushMessageExecutor);

//        shouHouPjService.insertSearchHistory(oaUserBO.getUserId(), shouhouId + "|2", "oaSearch");
        shouhou.setIsweixiu(shouhou.getIsweixiu() == null ? false : shouhou.getIsweixiu());
        ShouhouInfoRes shouhouInfoRes = new ShouhouInfoRes();
        //设置是否修改串号 查询风险确认书
        if(XtenantJudgeUtil.isJiujiMore()){
            shouhouInfoRes.setWhetherFixImei(whetherFixImei(shouhou));
            //查询风险确认书
            shouhouInfoRes.setShouHouRiskNotificationVOList(SpringUtil.getBean(ShouhouRiskNotificationService.class).selectRiskNotificationList(shouhou.getId()));
        }
        shouhouInfoRes.setWxpjList(createWxpjList(shouhou,shouhouInfoRes));
        //改价人员查询
        createUpdatePriceInfo(shouhou, shouhouInfoRes);
        //已取机,兼容旧数据
        if(Boolean.TRUE.equals(shouhou.getIsquji()) && shouhou.getWaiGuanStatus() == null){
            if(StrUtil.isBlank(shouhou.getWaiguan())){
                shouhou.setWaiGuanStatus(Shouhou.WaiGuanStatusEnum.WAN_HAO.getCode());
            }else{
                shouhou.setWaiGuanStatus(Shouhou.WaiGuanStatusEnum.MO_SHUN.getCode());
            }
        }
        BeanUtils.copyProperties(shouhou, shouhouInfoRes);
        List<ShouhouHardwareHistoryRes> res = shouhouExMapper.getHardwareHistory(shouhou.getImei());
        shouhouInfoRes.setWcount(res.size());
        shouhouInfoRes.setIsWaiguan(ObjectUtil.defaultIfNull(shouhou.getWaiGuanStatus(), Shouhou.WaiGuanStatusEnum.NO_SELECT.getCode()));
        // 将imei地址转换成http的url
        shouhouInfoRes.setImeifid(imageProperties.getImgUrlByFid(shouhou.getImeifid()));
        if (StringUtils.isEmpty(shouhouInfoRes.getArea()) && shouhouInfoRes.getAreaid() != null && shouhouInfoRes.getAreaid() != 0) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(shouhouInfoRes.getAreaid());
            if (ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) {
                shouhouInfoRes.setArea(areaInfoR.getData().getArea());
            }
        }
        begin = System.currentTimeMillis();

        //会员等级
        if (shouhouInfoRes.getUserid() != null) {
            R<MemberBasicRes> memberBasicResR = memberClient.getMemberBasicInfo(shouhouInfoRes.getUserid().intValue());
            if (ResultCode.SUCCESS == memberBasicResR.getCode() && memberBasicResR.getData() != null) {
                shouhouInfoRes.setUserClass(memberBasicResR.getData().getUserClass());
                //获取新的会员名称
                Integer xtenant = memberBasicResR.getData().getXtenant();
                UserClassConfig newUserClassName = UserClassEnum.getNewUserClassName(shouhouInfoRes.getUserClass(),xtenant,stringRedisTemplate,memberBasicResR.getData().getAreaId(),areaInfoClient);
                //兜底处理
                if (ObjectUtil.isEmpty(newUserClassName)) {
                    shouhouInfoRes.setUserClassName(EnumUtil.getMessageByCode(UserClassEnum.class, memberBasicResR.getData().getUserClass()));
                }else {
                    shouhouInfoRes.setUserClassName(newUserClassName.getUserClassName());
                }

            }
        }
        log.info("会员等级 执行时长>>>" + (System.currentTimeMillis() - begin));

        if (StringUtils.isNotEmpty(shouhouInfoRes.getMobile())) {

            String ch999User = baseMapper.getCh999User(shouhouInfoRes.getMobile(), jiujiSystemProperties.getOfficeName());
            if (StringUtils.isNotEmpty(ch999User)) {
                shouhouInfoRes.setCh999User(ch999User);
            }
        }
        ShouhouBo info = this.getOne(shouhouId);

        shouhouInfoRes.setUsername2(info.getUsername2());
        shouhouInfoRes.setUserMobile(info.getUserMobile());
        Boolean printFlag = baseMapper.getPrintFlag(shouhouId);
        printFlag = !Objects.isNull(printFlag);
        shouhouInfoRes.setPrintFlag(printFlag);

        //获取手机号码归属地
        if (StringUtils.isNotEmpty(info.getMobile()) && info.getMobile().length() >= 7){
            String phoneAddress = baseMapper.getPhoneAddressByPhoneNumber(info.getMobile().substring(0,7));
            shouhouInfoRes.setUserPhoneAddress(phoneAddress);
        }

        if (StringUtils.isNotEmpty(info.getShqd2Name())) {
            ShouhouInfoRes.ShouhouQudao qudaoInfo = new ShouhouInfoRes.ShouhouQudao();
            qudaoInfo.setShqdId(info.getShqdId());
            qudaoInfo.setShqd2Id(info.getShqd2Id());
            qudaoInfo.setShqd2Name(info.getShqd2Name());
            qudaoInfo.setQuDaoInUser(info.getQuDaoInUser());
            qudaoInfo.setQudaoAddTime(info.getQudaoAddTime());
            shouhouInfoRes.setQudaoInfo(qudaoInfo);
        }

        shouhouInfoRes.setIsqujiE(info.getIsqujiE());
        shouhouInfoRes.setIsxinjidan(shouhouInfoRes.getUserid() != null && shouhouInfoRes.getUserid() == 76783 ? "现货" : "客户");
        //插入新的搜索记录

        //维修配件
        begin = System.currentTimeMillis();
        List<HexiaoBo> hexiaoBoList = wxkcoutputService.getHexiao(shouhouId);
        //维修配件增加维修配件订购申请数据 https://jiuji.yuque.com/staff-fwzd6v/cvecc8/ir6478o1ggt46ex2?singleDoc#
        hexiaoBoList = Optional.ofNullable(hexiaoBoList).orElse(new ArrayList<>());
        List<Integer> hxIdList = hexiaoBoList.stream().map(HexiaoBo::getId).collect(Collectors.toList());
        // 批量获取订购信息
        Map<Integer, ShouhouApply> hxIdApplyMap = CommonUtils.bigDataInQuery(hxIdList, ids ->
                        shouhouApplyService.lambdaQuery().in(ShouhouApply::getOutputId, ids)
                        .select(ShouhouApply::getId, ShouhouApply::getOutputId, ShouhouApply::getBuyType).list())
                .stream()
                .collect(Collectors.toMap(ShouhouApply::getOutputId, Function.identity(), (v1, v2) -> v1));
        hexiaoBoList.forEach(v -> {
            v.setBeihuoStats(Boolean.TRUE.equals(v.getIslockc()) ? NumberConstant.EIGHT : NumberConstant.NINE);
            v.setIsShouhouApply(false);
            Optional.ofNullable(hxIdApplyMap.get(v.getId())).ifPresent(apply ->{
                v.setApplyId(apply.getId());
                v.setOrderType(ObjectUtil.defaultIfNull(apply.getBuyType(), 0) + 1);
            });
        });
        List<HexiaoBo> shouhouApplyHexiaoList = shouhouApplyService.getShouhouApplyHexiaoBo(shouhouId);
        hexiaoBoList.addAll(shouhouApplyHexiaoList);
        //如果存在生效的报销申请,不允许撤销
        Map<Integer, Integer> waisongHexiaoMap = Optional.ofNullable(hexiaoBoList).filter(CollUtil::isNotEmpty)
        .map(hbList -> SpringUtil.getBean(ShouhouWaisongHexiaoService.class)
                .lambdaQuery()
                .eq(WaisongHexiaoPo::getWxId, shouhouId)
                .in(WaisongHexiaoPo::getWxkId, hbList.stream().map(HexiaoBo::getId).collect(Collectors.toList()))
                .eq(WaisongHexiaoPo::getDel, false)
                .ne(WaisongHexiaoPo::getStatus, WaisongHexiaoPo.StatusEnum.DELETED.getCode())
                // 只查询 wxkId 和 id
                .select(WaisongHexiaoPo::getWxkId, WaisongHexiaoPo::getId)
                .list().stream().collect(Collectors.toMap(WaisongHexiaoPo::getWxkId,WaisongHexiaoPo::getId, (v1,v2) -> v1)))
                .orElse(Collections.emptyMap());
        hexiaoBoList.forEach(hxb->{
            //设置取消属性
            hxb.setCancelSelect(DecideUtil.iif(ShouhouConstants.CANCEL_REFUND_PRICE_CIDS.contains(hxb.getCid()),1,0));
            //设置是否有报账id
            hxb.setWaiSongId(waisongHexiaoMap.get(hxb.getId()));
        });
        shouhouInfoRes.setHexiaoTabs(hexiaoBoList);
        List<String> currUserRank = oaUserBO.getRank();
        if (CollectionUtils.isNotEmpty(hexiaoBoList)) {
            BigDecimal allPrice = BigDecimal.ZERO;
            BigDecimal allPrice1 = BigDecimal.ZERO;
            for (HexiaoBo e : hexiaoBoList) {
                allPrice = allPrice.add(e.getPrice()).add(e.getPriceGs());
                allPrice1 = allPrice1.add(e.getPrice1()).add(e.getPriceGs());
            }

            ShouhouInfoRes.HexiaoSum hexiaoSum = new ShouhouInfoRes.HexiaoSum();
            //改价权限百分比
            Double ed = 0.00;
            //改价固定值
            boolean isFixedAmount = false;
            if (currUserRank.contains("6g3")) {
                ed = 1.0;
            } else if (XtenantEnum.isJiujiXtenant() && currUserRank.contains("6g2")) {
                isFixedAmount = true;
                ed = 100.0;
            } else if (XtenantEnum.isJiujiXtenant() && currUserRank.contains("6g1")) {
                isFixedAmount = true;
                ed = 50.0;
            } else if (currUserRank.contains("6g2")) {
                ed = 0.5;
            } else if (currUserRank.contains("6g1")) {
                ed = 0.2;
            }

            BigDecimal edBigDecimal = BigDecimal.valueOf(ed);
            BigDecimal totalProfit = allPrice1.subtract(info.getCostprice());
            BigDecimal youhuiM = DecideUtil.iif(isFixedAmount, () -> NumberUtil.min(edBigDecimal, totalProfit), () -> totalProfit.multiply(edBigDecimal))
                    .subtract((allPrice1.subtract(allPrice)));
            if (youhuiM.compareTo(BigDecimal.ZERO) < 0) {
                youhuiM = BigDecimal.ZERO;
            }

            hexiaoSum.setAllPrice(allPrice);
            hexiaoSum.setSyYouhuiM(ed == 1.0 ? "无限制" : ed == 0 ? "0元" : youhuiM.toString() + "元");
            hexiaoSum.setYhPrice(allPrice1.subtract(allPrice));
            shouhouInfoRes.setHexiaoSum(hexiaoSum);
        }

        log.info("维修配件 执行时长>>>" + (System.currentTimeMillis() - begin));
        Optional<List<FileReq>> fileCacheOpt = Optional.ofNullable(stringRedisTemplate.opsForValue().get(StrUtil.format(RedisKeys.SHOUHOU_ATTACHMENTS_ADD_CACHE, shouhouId)))
                .map(fc -> JSON.parseObject(fc, new TypeReference<List<FileReq>>(){}));
        //附件 xxk 附件查询修改为从库
        CompletableFuture<List<FileReq>> filesFuture = CompletableFuture.supplyAsync(() -> fileCacheOpt.map(fcList -> fcList.stream()
                .filter(f -> Objects.equals(f.getKind(), 0)).collect(Collectors.toList()))
                .orElseGet(()->
                MultipleTransaction.query(DataSourceConstants.CH999_OA_NEW, () -> attachmentsService.getAttachmentsList(shouhouId, AttachmentsEnum.SHOUHOU.getCode(), 0,null))));
        //files2 xxk 附件查询修改为从库
        CompletableFuture<List<FileReq>> attachmentsFuture = CompletableFuture.supplyAsync(() -> fileCacheOpt.map(fcList -> fcList.stream()
                .filter(f -> Objects.equals(f.getKind(), 1)).collect(Collectors.toList()))
                .orElseGet(()->
                MultipleTransaction.query(DataSourceConstants.CH999_OA_NEW, () -> attachmentsService.getAttachmentsList(shouhouId, AttachmentsEnum.SHOUHOU.getCode(), 1,null))));
        List<FileReq> attachments;
        //微信绑定url
        String wxBindUrl;
        final CompletableFuture<String> wxBindUrlFuture = CompletableFuture.supplyAsync(() -> weixinUserService.getWxBindUrl(Optional.ofNullable(info.getUserid()).map(Long::intValue).orElse(0), shouhouInfoRes.getAreaid(), String.valueOf(shouhouId), NumberConstant.TWO), pushMessageExecutor);
        shouhouInfoRes.setNowareaId(info.getNowarea());



        //当前地区
        Optional<AreaInfo> nowAreaInfoOpt = Optional.empty();
        if (info.getNowarea() != null) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(info.getNowarea());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                nowAreaInfoOpt = Optional.ofNullable(areaInfoR.getData());
                shouhouInfoRes.setNowarea(nowAreaInfoOpt.map(AreaInfo::getArea).orElse(null));
            }
        }

        if (info.getTuihuanKind() != null) {
            shouhouInfoRes.setTuihuanKind(info.getTuihuanKind());
            shouhouInfoRes.setTuihuanKindText(EnumUtil.getMessageByCode(TuihuanKindEnum.class, info.getTuihuanKind()));
        }
        begin = System.currentTimeMillis();
        if (info.getAreaZh() != null && info.getAreaZh() != 0) {
            shouhouInfoRes.setAreaZh(info.getAreaZh());
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(info.getAreaZh());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                shouhouInfoRes.setAreaZhName(areaInfoR.getData().getArea());
            }
        }

        if (info.getToareaZh() != null && info.getToareaZh() != 0) {
            shouhouInfoRes.setToareaZh(info.getToareaZh());
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(info.getToareaZh());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                shouhouInfoRes.setToareaZhName(areaInfoR.getData().getAreaName());
                shouhouInfoRes.setToareaZhText(areaInfoR.getData().getArea());
            }
        }
        log.info("转地区 执行时长>>>" + (System.currentTimeMillis() - begin));
        //重大办
        begin = System.currentTimeMillis();
        ShouHouImportantBo zdb = baseMapper.isSubCollectZdb(shouhouId);
        if (zdb != null) {
            shouhouInfoRes.setZdb(zdb);
        }
        log.info("重大办 执行时长>>>" + (System.currentTimeMillis() - begin));
        // 订单关注情况
        begin = System.currentTimeMillis();
        Boolean isSubCollect = this.isSubCollect(shouhouId, oaUserBO.getUserId(), SubCollectTypeEnum.WX.getCode());
        //  订单是否存在接待记录
        Boolean isSubReception = subReceptionService.isSubReception(shouhouId, 2);
        shouhouInfoRes.setIsSubCollect(isSubCollect);
        shouhouInfoRes.setIsSubReception(isSubReception);
        log.info("订单关注情况 执行时长>>>" + (System.currentTimeMillis() - begin));
        //是否有提醒
        begin = System.currentTimeMillis();
        List<ShouhouTixing> txList = shouhouTixingService.getShouhouTixing(shouhouId);
        shouhouInfoRes.setIsTx(CollectionUtils.isNotEmpty(txList));
        if (CollectionUtils.isNotEmpty(txList)) {
            shouhouInfoRes.setShouhouTx(txList.get(0));
        }
        log.info("是否有提醒 执行时长>>>" + (System.currentTimeMillis() - begin));
        //ShouhouRomUpgrade 获取容量升级物流信息
        List<ShouhouRomUpgrade> sruList = shouhouRomUpgradeService.list(new QueryWrapper<ShouhouRomUpgrade>().lambda().eq(ShouhouRomUpgrade::getShouhouId, shouhouId));
        if (CollectionUtils.isNotEmpty(sruList)) {
            shouhouInfoRes.setExpressInfo(sruList.get(0));
        }
        //区域对象信息
        begin = System.currentTimeMillis();
        if (info.getNowarea() != null) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(info.getNowarea());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                shouhouInfoRes.setAreaInfoOne(areaInfoR.getData());
            }
        }
        AreaInfo areaSubject = SpringUtil.getBean(ShouhouYuyueService.class).initializationAreaSubject(info.getNowarea());
        shouhouInfoRes.setAreaSubject(areaSubject);
        log.info("区域对象信息 执行时长>>>" + (System.currentTimeMillis() - begin));

        begin = System.currentTimeMillis();
        //根据维修单编号获取中邮送修人客户姓名及联系电话
        R<ShouhouBusinessinfo> ShouhouBusinessinfoR = shouhouBusinessinfoService.getZySxUserinfo(shouhouId);
        if (ShouhouBusinessinfoR.getCode() == ResultCode.SUCCESS && ShouhouBusinessinfoR.getData() != null) {
            shouhouInfoRes.setBItem(ShouhouBusinessinfoR.getData());
        }
        log.info("中邮送修人 执行时长>>>" + (System.currentTimeMillis() - begin));
        //售后维修单赠品
        begin = System.currentTimeMillis();
        ShouHouZengpinBo zengPin = shouhouExService.getZengpinByShouhouid(shouhouId, 0);
        ShouHouZengpinBo zengPin1 = shouhouExService.getZengpinByShouhouid(shouhouId, 1);
        addCutScreenButton(zengPin,shouhou.getPpriceid(),shouhouId);
        addCutScreenButton(zengPin1,shouhou.getPpriceid(),shouhouId);
        shouhouInfoRes.setZengPin(zengPin);
        shouhouInfoRes.setZengPin1(zengPin1);
        log.info("售后维修单赠品 执行时长>>>" + (System.currentTimeMillis() - begin));
        //获取取机已审核信息
        begin = System.currentTimeMillis();
        String checkInfo = shouhouExService.getCheckInfo(shouhouId, info);
        String qjCheck = shouhouExService.getCheckIng(shouhouId, info);
        shouhouInfoRes.setCheckInfo(checkInfo);
        shouhouInfoRes.setQjCheck(qjCheck);
        log.info("取机已审核信息 执行时长>>>" + (System.currentTimeMillis() - begin));

        //库存ppids
        begin = System.currentTimeMillis();
        List<HexiaoBo> hxList = wxkcoutputService.getHexiao(shouhouId);
        List<Integer> kcOutPpids = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(hxList)) {
            kcOutPpids = hxList.stream().filter(e -> e.getPpid() != null).map(HexiaoBo::getPpid).collect(Collectors.toList());
            shouhouInfoRes.setKcOutPpids(kcOutPpids);
        }
        log.info("库存ppids 执行时长>>>" + (System.currentTimeMillis() - begin));

        //服务出险记录
        begin = System.currentTimeMillis();
        List<ShouhouServiceOutBo> serviceOutInfo = shouhouExService.getServersList(info.getImei(), shouhouId);
        if (CollectionUtils.isNotEmpty(serviceOutInfo)) {
            serviceOutInfo = serviceOutInfo.stream().map(s -> {
                s.setServiceName(EnumUtil.getMessageByCode(BaoXiuTypeEnum.class, s.getServiceType()));
                return s;
            }).collect(Collectors.toList());
            shouhouInfoRes.setServiceOutInfo(serviceOutInfo);
        }
        log.info("服务出险记录 执行时长>>>" + (System.currentTimeMillis() - begin));

        //维修组
        begin = System.currentTimeMillis();
        List<WeixiuzuKindVo> wxzKind = weixiuzuKindService.getWxGroupList();
        shouhouInfoRes.setWxzKind(wxzKind);

        log.info("维修组 执行时长>>>" + (System.currentTimeMillis() - begin));

        //获取最后一条测试数据信息
        shouhouInfoRes.setIsNewTestProcess(false);
        if (XtenantEnum.isJiujiXtenant() && Optional.ofNullable(shouhouId).orElse(0) > shouhouTestInfoConfig.getNewTestShouhouId()) {
            shouhouInfoRes.setIsNewTestProcess(true);
            ShouhouTestResultInfoService shouhouTestResultInfoService = SpringUtil.getBean(ShouhouTestResultInfoService.class);
            ShouhouTestResultInfo testResultInfo = shouhouTestResultInfoService.getLastResultByShouhouId(shouhouId);
            shouhouInfoRes.setShouhouTestResultInfo(testResultInfo);
            shouhouInfoRes.setIsShowAfterRepairTest(!Boolean.TRUE.equals(shouhouInfoRes.getIsquji()) && Objects.nonNull(shouhouTestResultInfoService.getLastResultByTypeAndShouhouId(ShouhouTestTypeEnum.BEFORE_REPAIRING.getCode(),shouhouId)));
            shouhouInfoRes.setIsShowBeforeRepairTest(!Boolean.TRUE.equals(shouhouInfoRes.getIsquji()) && Objects.isNull(shouhouTestResultInfoService.getLastResultByTypeAndShouhouId(ShouhouTestTypeEnum.AFTER_REPAIRING.getCode(),shouhouId)));
        } else {
            ShouhoutestInfo testInfo = shouhouExService.getLastTestInfoByShId(shouhouId);
            if (testInfo != null) {
                shouhouInfoRes.setTestInfo(testInfo);
            }
        }
        //所有故障类型
        shouhouInfoRes.setTroubleList(shouhouExService.getTroubleList());
        shouhouInfoRes.setTroubleIds(shouhouExService.getShouhouTroubles(shouhouId));

        // fuwuShowType
        Integer fuwuShowType = 0;
        ShouhouFuwupic shPic = shouhouFuwupicService.getFuwuPicInfo(shouhouId);

        Integer baoxiu = shouhouInfoRes.getBaoxiu() == null ? 0 : shouhouInfoRes.getBaoxiu();
        if (baoxiu != 2) {
            fuwuShowType = 1;
        }
        if (shPic != null) {
            fuwuShowType = 2;
            ShouhouFuwuDrRes fuwuDr = new ShouhouFuwuDrRes();
            fuwuDr.setFuwuPicFid(shPic.getFid());
            fuwuDr.setFuwuPicQuji(shPic.getFkind() == 2 || StringUtils.isNotEmpty(shPic.getFid()) && shouhouInfoRes.getServiceType() != 0);
            shouhouInfoRes.setFuwuDR(fuwuDr);
        }
        shouhouInfoRes.setFuwuShowType(fuwuShowType);
        String wxConfig = info.getWxConfig();

        //wxConfig 维修机型配置
        WxConfigRes wxconfig = new WxConfigRes();
        if (StringUtils.isNotEmpty(wxConfig)) {
            // id|cfg1|cfg2|cfg3
            String[] arr = wxConfig.split("\\|");
            if (arr.length > 0) {
                wxconfig = wxconfigoptionService.getWxConfig(Integer.valueOf(arr[0]));
            } else {
                //老系统的一个bug  售后单没有配置过 但是因为代码bug 默认给了第一个
                wxconfig = wxconfigoptionService.getWxConfig(1);
            }
            if (arr.length > 1) {
                wxconfig.setConfig1Options(wxconfigoptionService.getWxConfigOptionBy(arr[1]));
            }
            if (arr.length > 2) {
                wxconfig.setConfig2Options(wxconfigoptionService.getWxConfigOptionBy(arr[2]));
            }
            if (arr.length > 3) {
                wxconfig.setConfig3Options(wxconfigoptionService.getWxConfigOptionBy(arr[3]));
            }
            if (arr.length > 4) {
                wxconfig.setConfig4Options(wxconfigoptionService.getWxConfigOptionBy(arr[4]));
            }
        }
        shouhouInfoRes.setWxconfig(wxconfig);
        //获取处理进程添加处发送sms
        String sms7 = shouhouMsgService.getMsms7();
        shouhouInfoRes.setMSms7(StringUtils.isEmpty(sms7) ? "" : sms7);
        //风险告知书按钮,按钮显示条件（以下为且关系）：（1）维修单为：硬件维修单（2）维修单未取机（3）排除现货接件的维修单
        if (XtenantEnum.isJiujiXtenant()) {
            boolean isShowRiskNotice = !ObjectUtil.defaultIfNull(shouhou.getIssoft(),Boolean.FALSE) && !ObjectUtil.defaultIfNull(shouhou.getIsquji(),Boolean.FALSE);
            isShowRiskNotice = isShowRiskNotice
                                    && !BbsxpUserIdConstants.XIAN_HUO.equals(Convert.toInt(shouhouInfoRes.getUserid()))
                                    && !BigShouhouOrderTypeEnum.DJI.equals(getOrderType(shouhouId));
            shouhouInfoRes.setIsShowRiskNotice(isShowRiskNotice);
            //查询当前维修单关联的返修单
            if (!Objects.equals(SPOT_GOODS_USER_ID, shouhouInfoRes.getUserid()) && Boolean.TRUE.equals(shouhouInfoRes.getXianshi())) {
                List<Integer> repairOrderIdList = this.baseMapper.getShouhouIdByRepairOrderId(shouhouInfoRes.getId());
                if (CollectionUtils.isNotEmpty(repairOrderIdList)) {
                    shouhouInfoRes.setCurrentRepairOrderIds(StrUtil.join(",", repairOrderIdList));
                }
            }
        }
        R<ShouhouInfoRes> result = R.success(shouhouInfoRes);
        if (CollectionUtils.isNotEmpty(kcOutPpids)) {
            Duration duration = Duration.between(LocalDateTime.now(), (info.getOfftime() == null ? LocalDateTime.now() : info.getOfftime()));
            if ((areaSubject.getKind1() != null && areaSubject.getKind1() == 1 || areaSubject.getXtenant() == JiujiTenantEnum.JIUJI_TENANT_YAYA.getCode().intValue()) && (info.getIsqujiE() == 0 && duration.toDays() == 0)) {
                List<Integer> finalKcOutPpids = kcOutPpids;
                List<ShouhouServiceConfig> configList = DecideUtil.iif(XtenantEnum.isJiujiXtenant(),()->{
                    R<List<ShouhouServiceConfig>> configListR = shouhouServiceConfigService.getShouhouServiceConfigs(Optional.ofNullable(shouhou.getProductId()).orElse(0L),finalKcOutPpids);
                    result.addAllBusinessLog(configListR.businessLogs());
                    return configListR.getData();
                },()->shouhouServiceConfigService.getShouhouServicesList(finalKcOutPpids));

                if (CollUtil.isNotEmpty(configList)) {
                    String host = sysConfigService.getValueByCode(SysConfigConstant.M_URL);

                    List<Integer> servicePpids = configList.stream().map(ShouhouServiceConfig::getServicePpid).distinct().collect(Collectors.toList());
                    if(!XtenantEnum.isJiujiXtenant()){
                        List<Productinfo> list = productinfoService.list(new LambdaQueryWrapper<Productinfo>().in(Productinfo::getPpriceid, servicePpids));

                        if (CollectionUtils.isNotEmpty(list)) {

                            configList.stream().map(item -> {
                                Integer serviceType = item.getServiceType();
                                ServiceTypeEnum serviceEnum = EnumUtil.getEnumByCode(ServiceTypeEnum.class, serviceType);
                                String url = host + serviceEnum.getUrl();
                                item.setUrl(url);
                                item.setDescription(serviceEnum.getMessage() + "服务介绍");
                                item.setProductColor(
                                        Optional.of(list.stream().filter(t -> Objects.equals(t.getPpriceid(), item.getPpid())).findFirst().orElseGet(Productinfo::new)).get()
                                                .getProductColor());
                                return item;
                            }).collect(Collectors.toList());
                        }

                    }
                    shouhouInfoRes.setConfigList(configList);
                }
            }
        }

        //总计维修款特殊处理
        shouhouInfoRes.setFeiyong(shouhouInfoRes.getFeiyong() == null ? BigDecimal.ZERO : shouhouInfoRes.getFeiyong());
        List<ShouhouHuishou> huishouPj = shouhouHuishouService.getHuishouListBy(shouhouId);
        BigDecimal huishouPjPrice = huishouPj.stream().filter(e -> e.getPrice() != null).map(e -> e.getPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal youhuiFeiyong = shouhouInfoRes.getYouhuifeiyong() == null ? BigDecimal.ZERO : shouhouInfoRes.getYouhuifeiyong();
        BigDecimal memberDiscoutAmount = baseMapper.getMemberDiscoutAmount(shouhouId);
        BigDecimal totalFeiyong = shouhouInfoRes.getFeiyong().add(shouhouInfoRes.getYouhuifeiyong() == null ? BigDecimal.ZERO : shouhouInfoRes.getYouhuifeiyong())
                .add(huishouPjPrice).add(memberDiscoutAmount);
        BigDecimal yifum = shouhouInfoRes.getYifum() == null ? BigDecimal.ZERO : shouhouInfoRes.getYifum();
        BigDecimal feiyong = shouhouInfoRes.getFeiyong() == null ? BigDecimal.ZERO : shouhouInfoRes.getFeiyong();

        String feiyongText = StrUtil.indexedFormat("{0,number,0.00}",ObjectUtil.defaultIfNull(shouhouInfoRes.getFeiyong(),BigDecimal.ZERO));

        if (youhuiFeiyong.add(huishouPjPrice).add(memberDiscoutAmount).compareTo(BigDecimal.ZERO) > 0) {
            feiyongText = StrUtil.indexedFormat("{0,number,0.00}",totalFeiyong)
                    + DecideUtil.iif(youhuiFeiyong.compareTo(BigDecimal.ZERO) > 0 ,()-> StrUtil.indexedFormat("-{0,number,0.00}(优惠码)",youhuiFeiyong),()-> "")
                    + DecideUtil.iif(huishouPjPrice.compareTo(BigDecimal.ZERO) > 0 ,()-> StrUtil.indexedFormat("-{0,number,0.00}(旧件回收抵扣)",huishouPjPrice) ,()-> "")
                    + DecideUtil.iif(memberDiscoutAmount.compareTo(BigDecimal.ZERO) > 0,()->StrUtil.indexedFormat("-{0,number,0.00}(会员折扣)",memberDiscoutAmount),()->"")
                    + "=" + shouhouInfoRes.getFeiyong().toString();
        }
        shouhouInfoRes.setFeiyongInfoText(feiyongText);
        shouhouInfoRes.setYingfuM(feiyong.subtract(yifum));

        //维修成本，前端要根据角色做显示隐藏控制，暂时放到后端
        if (!currUserRank.contains("58")) {
            shouhouInfoRes.setCostprice(BigDecimal.ZERO);
        }
        //判断购买地区
        if (CommenUtil.isNullOrZero(info.getBuyareaid()) && CommenUtil.isNotNullZero(info.getSubId())) {
            //表示关联了订单，但是没有购买地区(有可能是退换机生成的维修单)
            //更改售后单购买地区
            shouhouExService.changeBuyAreaId(shouhouId, info.getSubId(), info.getIshuishou());
        }

        //获取购买地区门店信息
        Integer buyAreaId = getBuyAreaIdByShouhouId(shouhouId);
        if (buyAreaId != null) {
            shouhouInfoRes.setBuyareaid(buyAreaId);
        }
        Optional<AreaInfo> buyAreaInfoOpt = Optional.empty();
        if (shouhouInfoRes.getBuyareaid() != null) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(shouhouInfoRes.getBuyareaid());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                buyAreaInfoOpt = Optional.ofNullable(areaInfoR.getData());
                shouhouInfoRes.setBuyarea(buyAreaInfoOpt.map(AreaInfo::getArea).orElse(null));
            }
        }

        //超时天数计算，前端使用
        if (shouhouInfoRes.getOfftime() != null) {
            Duration duration = Duration.between(LocalDateTime.now(), shouhouInfoRes.getOfftime());
            shouhouInfoRes.setOffDays(Math.abs(duration.toDays()));
        }
        if (CommenUtil.isNullOrZero(shouhouInfoRes.getServiceType())) {
            shouhouInfoRes.setServiceType(null);
        }

        try {
            attachments = attachmentsFuture.get(5, TimeUnit.SECONDS);
            List<FileReq> files = filesFuture.get(5, TimeUnit.SECONDS);
            if (CollectionUtils.isNotEmpty(files)) {
                List<FileReq> files3 = files.stream().filter(file -> Objects.equals(file.getKind1(), 8) || Objects.equals(file.getKind1(), 9)).collect(Collectors.toList());
                shouhouInfoRes.setFiles3(files3);
                //折叠屏所需要文件图片
                List<FileReq> filesFoldscreen = files.stream().filter(file -> {
                    Integer kind1 = Optional.ofNullable(file.getKind1()).orElse(Integer.MIN_VALUE);
                    return Objects.equals(kind1, 10) || Objects.equals(kind1, 11);
                }).collect(Collectors.toList());
                shouhouInfoRes.setFilesFoldScreen(filesFoldscreen);
                //是否需要remove备用机附件待定
            }
            //是否是识别码加单 不会为空,sql已经做了处理 xxk
            shouhouInfoRes.setIsCodeSub(baseMapper.isCodeAddSub(shouhouId));
            shouhouInfoRes.setAttachments(attachments);
            shouhouInfoRes.setIsNew(Boolean.FALSE);
            if (CollectionUtils.isNotEmpty(files)) {
                files.stream().forEach(f -> {
                    if (CommenUtil.isNotNullZero(f.getKind1())) {
                        shouhouInfoRes.setIsNew(Boolean.TRUE);
                    }
                });
                files.removeIf(t -> (t.getKind1() != null && t.getKind1() > 6));
            }
            shouhouInfoRes.setFiles(files);
            wxBindUrl = wxBindUrlFuture.get();
            shouhouInfoRes.setWxBindUrl(wxBindUrl);
        } catch (RuntimeException | TimeoutException | ExecutionException e) {
            String msg = StrUtil.format("{}获取附件files2出错,售后id: {}", XtenantEnum.getTenantName(), shouhouId);
            log.error(msg, e);
        } catch (InterruptedException ex){
            String msg = StrUtil.format("{}获取附件files2出错,售后id: {},编号:1173", XtenantEnum.getTenantName(), shouhouId);
            log.error(msg, ex);
            Thread.currentThread().interrupt();
        }
        //根据出现服务的类型获取服务名称
        String messageByCode = EnumUtil.getMessageByCode(BaoXiuTypeEnum.class, shouhou.getServiceType());
        if (StringUtils.isNotEmpty(messageByCode)){
            shouhouInfoRes.setServiceName(EnumUtil.getMessageByCode(BaoXiuTypeEnum.class,shouhou.getServiceType()));
        }
        //折扣信息返回
        shouhouInfoRes.setDiscountInfo(DiscountInfoBo.wrapper(memberDiscountService.getMemberDiscount(Convert.toInt(shouhouInfoRes.getUserid()))));
        //企业粉丝加粉状态查询
        shouhouInfoRes.setIsCorporateFans(getCorporateFans(Convert.toInt(shouhouInfoRes.getUserid())));
        // 抖音团购优惠卷信息
        shouhouInfoRes.setLastDouYinCouponLog(baseMapper.getLastDouYinCouponLog(shouhouId, DouYinCouponLogRes.SubKindEnum.AFTERSALES.getCode()));
        shouhouInfoRes.setDouyinGoupSwitch(Objects.equals("1", sysConfigService.getValueByCode(SysConfigConstant.DOUYIN_GOUP_SWITCH)));
        //是否是后端员工
        AreaBelongsDcHqD1AreaId backEndInfo = areainfoService.getAreaBelongsDcHqD1AreaId(oaUserBO.getAreaId());
        shouhouInfoRes.setIsBackendStaff(oaUserBO.getRank().contains(ShouhouConstants.AFTER_EXCHANGE_SPECIAL_DEAL_PERMISSION));
        //查询不允许负库存出库的ppid
        result.put("ppidList", productKcServiceImpl.getPpidList());
        result.put("cidList", productKcServiceImpl.getCidList());
        //是否可以转出 xxk 现货
        result.put("isCanTransferOut", BbsxpUserIdConstants.XIAN_HUO.equals(Convert.toInt(shouhouInfoRes.getUserid()))
                //已修好 or 修不好
                && Arrays.asList(DealStatsEnum.YXH.getCode(), DealStatsEnum.XBH.getCode()).contains(shouhouInfoRes.getStats())
                //未取机
                && !Boolean.TRUE.equals(shouhouInfoRes.getIsquji()));
        if (XtenantEnum.isJiujiXtenant()) {
            List<Integer> sourcePpid = hxList.stream().map(HexiaoBo::getPpid).collect(Collectors.toList());
            List<Integer> targetPpid = DecideUtil.iif(XtenantEnum.isJiujiXtenant(),
                    () -> Optional.of(sourcePpid).filter(CollUtil::isNotEmpty)
                            .map(x -> webCloud.openingGetServices(x.stream()
                                    .map(StrUtil::toString).collect(Collectors.joining(",")), XtenantEnum.getXtenant()))
                            .filter(r -> r.getCode() == ResultCode.SUCCESS).map(Result::getData).map(psos -> psos.stream()
                                    .filter(pso -> Objects.nonNull(pso.getPpid()))
                                    .map(ProductServiceOpeningVO::getPpid).collect(Collectors.toList()))
                            .orElse(Collections.emptyList()), Collections::emptyList);
            targetPpid.retainAll(new ArrayList<>(sourcePpid));
            result.put("limitPpid", targetPpid);
        }else{
            result.put("limitPpid", new ArrayList<>());
        }
        setIsCanExchange(shouhouInfoRes, nowAreaInfoOpt, buyAreaInfoOpt);
        // 维修组数据纠正
        if (shouhouInfoRes.getWxzKind().stream()
                .noneMatch(wxz -> Objects.equals(wxz.getId(), shouhouInfoRes.getWeixiuzuid()))
                && shouhouInfoRes.getWxzKind().stream()
                .anyMatch(wxz -> Objects.equals(wxz.getId(), shouhouInfoRes.getWeixiuzuidJl()))) {
            shouhouInfoRes.setWeixiuzuid(shouhouInfoRes.getWeixiuzuidJl());
        }
        //接件时间在2021.09.01-2022.02.15之间  显示改文案
        Boolean aBoolean = DateUtil.checkCurrentTimeInTime(shouhou.getModidate(), LocalDate.of(2021, 9, 1), LocalDate.of(2022, 2, 15));
        if (Boolean.TRUE.equals(aBoolean) && XtenantEnum.isJiujiXtenant()){
            shouhouInfoRes.setMarkCopy("（2021年9月1日-2022年2月15日期间iPhone5—iPhone 11全系列设备维修，电池享两年质保，屏幕享180天人为损坏半价更换一次，请注意核实）");
        }
        /**
         * 售后详情信息扩展
         */
        setShouhouInfoExtend(shouhouInfoRes);

        return result;
    }

    private void addCutScreenButton(ShouHouZengpinBo zengPin,Integer ppriceid ,Integer shouhouId) {
        if (Objects.isNull(zengPin)){
            return;
        }
        Productinfo p = productinfoService.getProductinfoByPpid(zengPin.getPpriceid());
        //纳米防护膜分类可以切膜
        if (XtenantEnum.isJiujiXtenant() && "662".equals(Convert.toStr(p.getCid()))) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String platform = request.getHeader("Platform");
            if (StringUtils.isEmpty(platform) || !platform.contains("MOA")) {
                R<String> hostR = sysConfigClient.getValueByCode(SysConfigConstant.OA_URL);
                if (hostR.getCode() == ResultCode.SUCCESS
                        && StringUtils.isNotEmpty(hostR.getData())) {
                    String url = hostR.getData() + SmallProRelativePathConstant.CUT_SCREEN_SHOUHOU_PC;
                    zengPin.setShowCutScreen(Boolean.TRUE);
                    zengPin.setCutScreenUrl(StrUtil.format(url, ppriceid, shouhouId));
                }
            } else {
                R<String> hostR = sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL);
                if (hostR.getCode() == ResultCode.SUCCESS
                        && StringUtils.isNotEmpty(hostR.getData())) {
                    String url = hostR.getData() + SmallProRelativePathConstant.CUT_SCREEN_SHOUHOU_MOA;
                    zengPin.setShowCutScreen(Boolean.TRUE);
                    zengPin.setCutScreenUrl(StrUtil.format(url, ppriceid, shouhouId));
                }
            }
        }
    }

    /**
     * 售后详情扩展表
     * @param shouhouInfoRes
     */
    private void setShouhouInfoExtend(ShouhouInfoRes shouhouInfoRes) {
        ProductTypeEnum productTypeEnum = getProductTypeEnum(shouhouInfoRes);
        shouhouInfoRes.setProductType(productTypeEnum.getCode());
        shouhouInfoRes.setProductTypeMsg(productTypeEnum.getMessage());
        try {
            Integer shouHouId = shouhouInfoRes.getId();
            //获取维修单类型
            Integer repairOrderType = this.baseMapper.selectRepairOrderType(shouHouId);
            //过滤了维修类型  因为大部分都是维修类型不需要特地展示
            if(!RepairOrderTypeEnum.REPAIR.getCode().equals(repairOrderType)){
                shouhouInfoRes.setRepairOrderType(RepairOrderTypeEnum.getNewMessageByCode(repairOrderType));
            }
            shouhouInfoRes.setOrderType(getOrderType(shouHouId).getCode());
            //获取员工信息
            //判断是否为员工
            Ch999UserBasicBO ch999UserBasicBO = getCh999UserBasicBO(shouhouInfoRes.getMobile());
            if(ObjectUtil.isNull(ch999UserBasicBO)){
                shouhouInfoRes.setIsStaffSub(Boolean.FALSE);
            } else {
                shouhouInfoRes.setIsStaffSub(Boolean.TRUE);
                //职务设置
                Optional.ofNullable(zhiWuService.getById(ch999UserBasicBO.getZhiwuId())).ifPresent(zhiWu -> shouhouInfoRes.setZhiwu(zhiWu.getName()));
            }
            //是否学生认证
            Integer userId = Convert.toInt(shouhouInfoRes.getUserid());
            List<Integer> coin9jis = this.baseMapper.selectCoin9jiByUserId(userId);
            if(CollectionUtils.isNotEmpty(coin9jis)){
                shouhouInfoRes.setStudentCertificationStatus(NumberConstant.ONE);
            }
            //维修配件条码查询
            List<LockWxpjBo> wxpjList = shouhouInfoRes.getWxpjList();
            if(CollectionUtils.isNotEmpty(wxpjList)){
                List<Integer> ppidList = wxpjList.stream().map(LockWxpjBo::getPpid).collect(Collectors.toList());
                Map<Integer, List<Productbarcode>> productbarcodeMap = CommonUtils.bigDataInQuery(ppidList, ids -> barcodeService.lambdaQuery()
                        .eq(Productbarcode::getOpXtenant, XtenantEnum.getXtenant())
                        .in(Productbarcode::getPpriceid, ids)
                        .list()).stream().collect(Collectors.groupingBy(Productbarcode::getPpriceid));
                wxpjList.forEach(item->{
                    List<String> collect = productbarcodeMap.getOrDefault(item.getPpid(),new ArrayList<>()).stream()
                            .sorted(Comparator.nullsLast(Comparator.comparing(Productbarcode::getIsDefault).reversed()))
                            .map(Productbarcode::getBarCode)
                            .collect(Collectors.toList());
                    item.setProductbarcodeList(collect);
                });
            }
            //发票金额板块
            InvoiceInfo invoiceInfo = new InvoiceInfo();
            TaxPiaoService taxPiaoService = SpringUtil.getBean(TaxPiaoService.class);
            List<TaxPiao> list = taxPiaoService.lambdaQuery().eq(TaxPiao::getSubId, shouHouId)
                    .eq(TaxPiao::getType, 1)
                    .notIn(TaxPiao::getFlag, Arrays.asList(-1,5,6))
                    .orderByDesc(TaxPiao::getId)
                    .list();
            if(CollectionUtils.isNotEmpty(list)){
                TaxPiao taxPiao = Optional.ofNullable(list.get(NumberConstant.ZERO)).orElse(new TaxPiao());
                invoiceInfo.setInvoiceState(taxPiao.getFlag());
                invoiceInfo.setInvoiceStateValue(TaxPiaoFlagEnum.getValue(taxPiao.getFlag()));
            } else {
                invoiceInfo.setInvoiceStateValue(TaxPiaoFlagEnum.NOT_INVOICED.getMessage());
            }
            Optional.ofNullable(SpringUtil.getBean(TaxPiaoMapper.class).selectTaxPiaoByUserId(userId)).ifPresent(taxPiaoUser -> {
                invoiceInfo.setInvoiceCount(taxPiaoUser.getInvoiceCount());
                invoiceInfo.setInvoiceTotalPrice(taxPiaoUser.getInvoiceTotalPrice());
            });
            shouhouInfoRes.setInvoiceInfo(invoiceInfo);


        }catch (Exception e){
            RRExceptionHandler.logError("售后详情扩展表异常", shouhouInfoRes, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }

    }

    @Override
    @NotNull
    public BigShouhouOrderTypeEnum getOrderType(Integer shouHouId) {
        return SpringContextUtil.reqCache(() -> {
            BigShouhouOrderTypeEnum orderTypeEnum = BigShouhouOrderTypeEnum.NORMAL;
            ShouhouExtendService shouhouExtendService = SpringUtil.getBean(ShouhouExtendService.class);
            if (shouhouExtendService.isDJIRepairOrder(null, shouHouId)) {
                orderTypeEnum = BigShouhouOrderTypeEnum.DJI;
            }
            return orderTypeEnum;
        }, RequestCacheKeys.SHOUHOU_SERVICE_GET_ORDER_TYPE, shouHouId);
    }

    @Override
    public ProductTypeEnum getProductTypeEnum(Shouhou shouhou) {
        //设置商品类型
        ProductTypeEnum productTypeEnum = ProductTypeEnum.NEW_MACHINE;
        if(IshuishouEnum.GOOD_PRODUCT.getCode().equals(shouhou.getIshuishou())){
            //良品
            productTypeEnum = ProductTypeEnum.GOOD_PRODUCT;
        } else if (IshuishouEnum.VALUE_ADDED_RECYCLING.getCode().equals(shouhou.getIshuishou())){
            //回收增值机
            productTypeEnum = ProductTypeEnum.VALUE_ADDED_RECYCLING;
        }else if(ObjectUtil.defaultIfNull(shouhou.getSubId(),0)<=0){
            // 外修
            productTypeEnum = ProductTypeEnum.EXTERNAL_REPAIR_MACHINE;
        }else if(ObjectUtil.defaultIfNull(shouhou.getBasketId(),0) >0 && SpringUtil.getBean(BasketService.class)
                .lambdaQuery().eq(Basket::getBasketId, shouhou.getBasketId())
                .eq(Basket::getType, BasketTypeEnum.BASKET_TYPE_DEFECT_MACHINE.getCode()).count()>0){
            //优品
            productTypeEnum = ProductTypeEnum.EXCELLENT_PRODUCT;
        }
        return productTypeEnum;
    }

    private void setIsCanExchange(ShouhouInfoRes shouhouInfoRes, Optional<AreaInfo> nowAreaInfoOpt, Optional<AreaInfo> buyAreaInfoOpt) {
        boolean isCanExchange = true;
//        if(XtenantEnum.isJiujiXtenant() && nowAreaInfoOpt.isPresent() && buyAreaInfoOpt.isPresent()){
//            AreaInfo nowAreaInfo = nowAreaInfoOpt.get();
//            AreaInfo buyAreaInfo = buyAreaInfoOpt.get();
//
//            isCanExchange = BusinessUtil.jiujiSameAuthArea(nowAreaInfo,buyAreaInfo);
//            if(!isCanExchange){
//                shouhouInfoRes.setExchangeMsg(StrUtil.format("设备购买地为{}，请在该门店后台下进行接件办理退换操作",buyAreaInfo.getArea()));
//            }
//        }
        shouhouInfoRes.setIsCanExchange(isCanExchange);
    }

    @Override
    public boolean getCorporateFans(Integer userId) {
        if(!XtenantEnum.isJiujiXtenant(XtenantEnum.getXtenant())){
            return false;
        }
        if (CommenUtil.isNullOrZero(userId)) {
            return false;
        }
        String corporateFans = baseMapper.getCorporateFans(userId);
        if (StrUtil.isNotBlank(corporateFans)){
            return true;
        }
        return false;
    }

    /**
     * 获取九机服务
     * @param shouhouId 售后id
     * @return
     */
    @Override
    public R<JiuJiFuWuChuXianRes> getJiuJiFuWu(Integer shouhouId) {
        JiuJiFuWuChuXianRes jiuJiFuWuChuXianRes = new JiuJiFuWuChuXianRes();
        ShouhouBo info = this.getOne(shouhouId);
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        //获取租户信息
        String xTenantName = sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.PRINT_NAME, oaUserBO.getXTenant());
        jiuJiFuWuChuXianRes.setTitle("设备购买"+xTenantName+"服务:");
        //获取九机服务,售后单服务是否在保查询通过接件时间进行校验
        SpringContextUtil.getRequest().ifPresent( request -> request.setAttribute(RequestAttrKeys.AFTER_MODIDATE,info.getModidate()));
        Integer userId = null;
        //通过订单号获取userid,避免查询到其他用户的单号
        if(ObjectUtil.defaultIfNull(info.getSubId(),0) >0){
            userId = SpringUtil.getBean(SubService.class).getUserIdBySubId(info.getSubId());
        }
        ServiceInfoVO record = serviceRecordService.getRecord(info.getImei(), userId, false);
        //取消保修显示
        List<JiuJiFuWuChuXianRes.Link> links = new ArrayList<>();
        record.getServiceVos().forEach(vo ->{
//            JiuJiFuWuChuXianRes.Link link = new JiuJiFuWuChuXianRes.Link();
            if ("bao_xiu".equals(vo.getType()) || EnumUtil.getEnumByCode(ServiceEnum.class, vo.getTypeCode()).getShouHouType() == null){
                vo.setDetailInfo(null);
            }
            //售后质保介绍
//            link.setLink("链接正在获取中....");
//            link.setType(vo.getType());
//            links.add(link);
        });
        jiuJiFuWuChuXianRes.setLinkList(links);
        List<Integer> selfClassCodeList = Arrays.asList(ServiceClassificationEnum.SELF.getCode(), ServiceClassificationEnum.LIMIT.getCode());
        jiuJiFuWuChuXianRes.setServiceList(record.getServiceVos().stream().filter(s -> s.getDetailInfo() != null
                && selfClassCodeList.contains(ObjectUtil.defaultIfNull(s.getDetailInfo().getClassification(),ServiceClassificationEnum.SELF.getCode())))
                .collect(Collectors.toList()));
        //九机服务code  特殊转换处理。
        jiuJiFuWuChuXianRes.getServiceList().forEach(s -> {
            if (CommenUtil.isNotNullZero(s.getTypeCode())){
                Optional<BaoXiuTypeEnum> shouHouType = Optional.ofNullable(EnumUtil.getEnumByCode(ServiceEnum.class, s.getTypeCode()).getShouHouType());
                if (shouHouType.isPresent()){
                    s.setTypeCode(shouHouType.get().getCode());
                    s.getDetailInfo().setTypeCode(shouHouType.get().getCode());
                }
            }
        });
        //获取九机服务的枚举类
        List<String> rank = oaUserBO.getRank();
        jiuJiFuWuChuXianRes.setBxTypeEnum(new LinkedList<>());
        if (rank.contains("jjfu")) {
            jiuJiFuWuChuXianRes.setBxTypeEnum(EnumUtil.toEnumVOList(BaoXiuTypeEnum.class));
        }
        //获取维修配件信息
        List<HexiaoBo> hexiaoBoList = wxkcoutputService.getHexiao(shouhouId);
        jiuJiFuWuChuXianRes.setMaintainTabs(hexiaoBoList);
        //图片示例
        jiuJiFuWuChuXianRes.setInstance("https://moa.ch999.com/static/760,25302dafe9a2fe");
        return R.success(jiuJiFuWuChuXianRes);
    }


    private R<String> checkLimitService(JiuJiFuWuChuXianReq req) {
        WxFeeBo wxFeeBo = Optional.ofNullable(req.getWxFeeBo()).orElse(new  WxFeeBo());
        Integer serviceRecordId = Optional.ofNullable(wxFeeBo.getServiceRecordId()).orElse(NumberConstant.ZERO);
        ServiceRecord serviceRecord = SpringUtil.getBean(ServiceRecordService.class).lambdaQuery().eq(ServiceRecord::getId, serviceRecordId)
                .eq(ServiceRecord::getClassification, ServiceClassificationEnum.LIMIT.getCode())
                .one();
        if(ObjectUtil.isNotNull(serviceRecord)){
            Integer serviceCount = serviceRecord.getServiceCount();
            if(serviceCount <= NumberConstant.ZERO){
                return R.error("该服务出险次数用完");
            }
            LocalDateTime endTime = serviceRecord.getEndTime();
            LocalDateTime startTime = serviceRecord.getStartTime();


        }
        return R.success("校验通过");
    }

    /**
     * 办理九机出险服务
     * @param req req
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> saveOrUpdateJiuJiFuWu(JiuJiFuWuChuXianReq req) {
        ShouhouInfoRes shouhouInfoRes = req.getShouhouInfoRes();
        if (shouhouInfoRes==null){
            return R.error("参数错误！");
        }

        if(CommenUtil.isNullOrZero(shouhouInfoRes.getServiceType())){
            return R.error("请选择出险的服务！");
        }
        R<String> checked = checkLimitService(req);
        if(!checked.isSuccess()){
            return checked;
        }

        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Optional<ServiceVO.DetailInfo> detailInfoOpt = Optional.empty();
        long xtenant = Namespaces.get();
        //增加高级权限后端校验
        ServiceInfoVO serviceInfo = CompletableFuture
                .supplyAsync(()->currentRequestComponent.invokeWithUser(xtenant,oaUserBO,user ->SpringUtil.getBean(ServiceRecordService.class)
                        .getRecord(shouhouInfoRes.getImei(), null, false)))
                .join();
        detailInfoOpt = serviceInfo.getServiceVos().stream().map(ServiceVO::getDetailInfo).filter(Objects::nonNull)
                //找生效中的保险
                .filter(dt -> Objects.equals(ServiceVO.DetailInfo.EffectiveEnum.ACTIVE.getCode(), dt.getEffective()))
                //找服务对应的售后服务类型
                .filter(dt -> Arrays.stream(ServiceEnum.values()).filter(se -> Objects.equals(se.getCode(), dt.getTypeCode()))
                        .findFirst().map(ServiceEnum::getShouHouType).filter(sht -> Objects.equals(sht.getCode(), shouhouInfoRes.getServiceType()))
                        //存在服务
                        .isPresent()).findFirst();
        //没有匹配的服务,但是没有高级权限
        if(!detailInfoOpt.isPresent() && !CollUtil.contains(oaUserBO.getRank(),RankEnum.JIUJI_SERVICE_ADVANCE.getCode())){
            throw new CustomizeException(StrUtil.format("你没有高级授权出险的权限,权值:{}",RankEnum.JIUJI_SERVICE_ADVANCE.getCode()));
        }
        AtomicReference<BigDecimal> newPrice = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> oldPrice = new AtomicReference<>(BigDecimal.ZERO);
        //当维修配件id不为空时 修改价格
        List<Integer> servicePjIds = new LinkedList<>();
        if (CommenUtil.isNotNullZero(req.getServiceTabsId())){
            //处理九机出险价格 当出九机服务的时候，修改价格
            if (!Arrays.asList(9,10,11).contains(shouhouInfoRes.getServiceType())){
                req.getShouhouInfoRes().getHexiaoTabs().forEach(item -> {
                    if (Objects.equals(item.getId(),req.getServiceTabsId())){
                        newPrice.set(Optional.ofNullable(item.getPrice1()).orElse(BigDecimal.ZERO));
                        item.setPrice(BigDecimal.ZERO);
                        servicePjIds.add(item.getId());
                    }
                });
            }else {
                //当九机服务为9,10,11时，进行半价处理
                req.getShouhouInfoRes().getHexiaoTabs().forEach(item -> {
                    if (Objects.equals(item.getId(),req.getServiceTabsId())){
                        newPrice.set(Optional.ofNullable(item.getPrice1()).orElse(BigDecimal.ZERO));
                        oldPrice.set(Optional.ofNullable(item.getPrice1()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(0.5)));
                        item.setPrice(oldPrice.get());
                        servicePjIds.add(item.getId());
                    }
                });
            }
        }
        //当配件ppid不为空时，则添加对应配件
        if(!BeanUtil.isEmpty(req.getWxFeeBo())){
            req.getWxFeeBo().setAreaId(oaUserBO.getAreaId());
            req.getWxFeeBo().setUser(oaUserBO.getUserName());
            req.getWxFeeBo().setXtenant(oaUserBO.getXTenant());

            //处理九机出险价格 当出九机服务的时候，修改价格
            if (!Arrays.asList(9,10,11).contains(shouhouInfoRes.getServiceType())){
                newPrice.set(Optional.ofNullable(req.getWxFeeBo().getPrice1()).orElse(BigDecimal.ZERO));
                req.getWxFeeBo().setPrice(BigDecimal.ZERO);

            }else {
                //当九机服务为9,10,11时，进行半价处理
                newPrice.set(Optional.ofNullable(req.getWxFeeBo().getPrice1()).orElse(BigDecimal.ZERO));
                oldPrice.set(Optional.ofNullable(req.getWxFeeBo().getPrice1()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(0.5)));
                req.getWxFeeBo().setPrice(oldPrice.get());
            }

            R<ShouhouCostPriceRes> shouhouCostPriceResR = ((ShouhouServiceImpl) AopContext.currentProxy())
                    .addCostPriceWithAutoBind(req.getWxFeeBo(),Boolean.FALSE,Boolean.TRUE);
            if (!shouhouCostPriceResR.isSuccess()){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error(shouhouCostPriceResR.getUserMsg());
            }
            servicePjIds.add(req.getWxFeeBo().getId());
        }

        if(CollUtil.isEmpty(servicePjIds) || CommenUtil.isNullOrZero(shouhouInfoRes.getServiceType())){
            throw new CustomizeException("出险的配件和服务标识都不能为空");
        }
        //先更新出险的配件标识,后面需要用到
        int ust = wxkcoutputService.updateServiceType(servicePjIds, shouhouInfoRes.getServiceType());
        if(ust<=0){
            throw new CustomizeException(StrUtil.format("维修配件id{},更新服务标识失败",servicePjIds.stream().map(Convert::toStr).collect(Collectors.joining(","))));
        }

        req.getShouhouInfoRes().setIsJiujiServiceFlag(true);
        //更改配件价格并记录日志
        R<Boolean> booleanR = ((ShouhouServiceImpl) AopContext.currentProxy())
                .saveOrUpdateShouhouInfo(req.getShouhouInfoRes());
        if (booleanR.getCode()!=0){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return booleanR;
        }
        AtomicReference<String> wxpjName = new AtomicReference<>();
        //获取维修配件名称
        if (CommenUtil.isNotNullZero(req.getServiceTabsId())) {
            List<HexiaoBo> hexiao = wxkcoutputService.getHexiao(req.getShouhouInfoRes().getId());
            hexiao.forEach(h -> {
                if (Objects.equals(h.getId(), req.getServiceTabsId())) {
                    wxpjName.set(h.getWxpjName()+",ID:"+h.getId()+",PPID:"+h.getPpid());
                }

            });
        } else {
            wxpjName.set(req.getWxFeeBo().getProductName()+",ID:"+req.getWxFeeBo().getPid()+",PPID:"+req.getWxFeeBo().getPpid());
        }

        this.saveShouhouLog(req.getShouhouInfoRes().getId(),
                StrUtil.format("{}{}服务 ,维修配件【{}】,价格由{}更改为{} {}{}{}"
                , detailInfoOpt.map(di -> ORDINARY_OUT_SERVICE).orElse(GAOJI_RANK_OUT_SERVICE)
                , req.getShouhouInfoRes().getServiceName() , wxpjName,newPrice.get().setScale(2,BigDecimal.ROUND_HALF_UP)
                , oldPrice.get().setScale(2,BigDecimal.ROUND_HALF_UP), OUT_SERVICE_IMIE_STRAT, shouhouInfoRes.getImei(), OUT_SERVICE_IMIE_END)
                , oaUserBO.getUserName());
        return R.success(Boolean.TRUE);
    }


    @Override
    public Boolean saveBatchShouhou(List<ShouhouReq> shList) {
        if (CollectionUtils.isEmpty(shList)) {
            return false;
        }
        for (ShouhouReq sh : shList) {
            this.saveShouhou(sh);
        }
        return true;
    }

    @Override
    public R<Boolean> updateDaojishi(Integer shouhouId, Integer day) {
        if (day < 1) {
            day = null;
        }
        if (shouhouId == null || shouhouId == 0) {
            return R.error("售后Id不能为空");
        }
        UpdateWrapper<Shouhou> up = new UpdateWrapper<>();
        up.lambda().set(Shouhou::getDaojishi, day).eq(Shouhou::getId, shouhouId);
        Boolean b = this.update(up);
        return R.success(b);
    }

    @Override
    public R<Boolean> nahou(Integer shouhouId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.error("请登陆");
        }
        Shouhou shouhou = baseMapper.selectById(shouhouId);
        if (shouhou == null) {
            return R.error("查无此记录");
        }
        Nahuoduilie nahuoduilie = nahuoduilieService.getNahouduilieByBasketId(shouhouId);
        if (shouhou.getIsquji() == null || !shouhou.getIsquji()) {
            if (nahuoduilie == null) {
                Integer id = nahuoduilieService.addNahouduilie(shouhouId, 7, oaUserBO.getAreaId(), oaUserBO.getUserName());
                Integer count = nahuoduilieService.count(new LambdaQueryWrapper<Nahuoduilie>().eq(Nahuoduilie::getAreaid, oaUserBO.getAreaId()).eq(Nahuoduilie::getIsna, 7).lt(Nahuoduilie::getAddtime, LocalDateTime.now()));
                return R.success("成功加入拿货队列，编号为:" + id + "， 您前面还有" + count + "个！", true);
            } else {
                if (nahuoduilie.getIsna() != null && nahuoduilie.getIsna() == 8) {
                    String natime = Duration.between(nahuoduilie.getNatime(), LocalDateTime.now()).toHours() < 12 ?
                            DateTimeFormatter.ofPattern("HHmmss").format(nahuoduilie.getNatime()) : DateUtil.localDateTimeToString(nahuoduilie.getNatime());
                    String msg = "已经取货，取货时间:" + natime + " 提交:" + nahuoduilie.getAdduser() + " 取货人:" + nahuoduilie.getNauser() + "，编号为:" + nahuoduilie.getOrderidd() + "!";
                    return R.success(msg, true);
                } else {
                    Duration duration = Duration.between(nahuoduilie.getAddtime(), LocalDateTime.now());
                    String addtime = duration.toHours() < 12 ?
                            DateTimeFormatter.ofPattern("HH:mm:ss").format(nahuoduilie.getAddtime()) : DateUtil.localDateTimeToString(nahuoduilie.getAddtime());
                    Long minute = duration.toMinutes();
                    Long hour = minute > 60 ? minute / 60 : 0;
                    String msg = hour > 0 ? hour + "小时" : "";
                    if (minute > 0) {
                        msg += minute % 60 + "分";
                    }
                    String retMsg = "已经在拿货中，加入时间:" + addtime + "(" + msg + "前)，提交:" + nahuoduilie.getAdduser() + " 编号为:" + nahuoduilie.getOrderidd() + "！";
                    return R.error(retMsg);
                }
            }
        } else {
            if (nahuoduilie != null) {
                String msg = "历史拿货记录，提交人:" + nahuoduilie.getAdduser() + "，加入时间:" + nahuoduilie.getAddtime() + "，是否取货:" + nahuoduilie.getIsna()
                        + "，取货人:" + nahuoduilie.getNauser() + "，取货时间:" + nahuoduilie.getNatime();
                return R.error(msg);
            } else {
                return R.error("已取走机器不可提交取机！");
            }
        }
    }

    @Override
    public R<Boolean> sendNumberCard(Integer shouhouId) {
        List<Shouhou> shouhouList = baseMapper.selectList(new LambdaQueryWrapper<Shouhou>().eq(Shouhou::getId, shouhouId));
        if (CollectionUtils.isEmpty(shouhouList) || shouhouList.get(0).getUserid() == null || shouhouList.get(0).getUserid() == 0L) {
            return R.error("用户信息查找失败");
        }
        Shouhou shouhou = shouhouList.get(0);
        Integer userId = shouhou.getUserid().intValue();
        if (redisTemplate.hasKey(RedisKeys.SHOUHOU_NUMBER_CARD_PERFIX + userId)) {
            return R.error("该客户已经发放过优惠码了");
        }
        R<MemberBasicRes> memberBasicResR = memberClient.getMemberBasicInfo(userId);
        if (ResultCode.SUCCESS != memberBasicResR.getCode() || memberBasicResR.getData() == null) {
            return R.error("用户信息查找失败");
        }
        MemberBasicRes memberInfp = memberBasicResR.getData();
        Integer total = 50;
        if (6 == memberInfp.getUserClass()) {
            total = 80;
        } else if (5 == memberInfp.getUserClass()) {
            total = 60;
        }
        //优惠码添加
        NumberCardReq numberCardReq = new NumberCardReq();
        numberCardReq.setGName("购机优惠码-售后推荐渠道");
        numberCardReq.setUserid(userId);
        numberCardReq.setCount(1);
        numberCardReq.setLimitprice(BigDecimal.valueOf(1000));
        numberCardReq.setLimit(1);
        numberCardReq.setLimit1(1);
        numberCardReq.setLimit2(false);
        numberCardReq.setTotal(BigDecimal.valueOf(total));
        numberCardReq.setStartTime(LocalDate.now());
        numberCardReq.setEndTime(LocalDate.now());
        try {
            R<List<Integer>> ret = numberCardService.addNumberCard(Arrays.asList(numberCardReq));
            if (ResultCode.SUCCESS == ret.getCode()) {
                redisTemplate.opsForValue().set(RedisKeys.SHOUHOU_NUMBER_CARD_PERFIX + userId.toString(), "1", 24, TimeUnit.HOURS);
            }
            return ResultCode.SUCCESS == ret.getCode() ? R.success("操作成功", true) : R.error(ret.getUserMsg());
        } catch (Exception e) {

            log.error("优惠码添加异常：{}", e.getMessage());
//            e.printStackTrace();
            return R.error(e.getMessage());
        }

    }

    @Override
    public R<Boolean> pushMessage(ShouhouMsgPushMessageBo pushmsg, Boolean isRecordLog) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        try {
            if (isRecordLog == null) {
                isRecordLog = true;
            }
            ShouhouMsgconfig shouhouMsgconfig = shouhouMsgconfigService.getShouhouMsgconfigById(pushmsg.getMsgId());
            if (shouhouMsgconfig == null) {
                return R.error("没有查询到消息模板");
            }

            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(pushmsg.getAreaId());
            if (ResultCode.SUCCESS != areaInfoR.getCode() || areaInfoR.getData() == null) {
                return R.error("查询不到相应门店");
            }

            //是否为排除的子租户
            boolean isExcludeXtenant = StrUtil.splitTrim(shouhouMsgconfig.getExcludeXtenant(), StringPool.COMMA)
                    .contains(Convert.toStr(XtenantEnum.getXtenant()));

            AreaInfo areaInfo = areaInfoR.getData();
            Long xtenant = Long.valueOf(areaInfo.getXtenant());
            String wxsendmsg = pushmsg.getPostmsg();
            //系统自动发送
            if (shouhouMsgconfig.getIssysmsg()) {
                wxsendmsg = shouhouMsgService.getPushMessageContent(pushmsg, areaInfo);
            }
            if (StringUtils.isEmpty(wxsendmsg)) {
                return R.error("消息内容为空");
            }
            if (xtenant == null) {
                xtenant = 0L;
            }
            R<String> urlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, Math.toIntExact(xtenant));
            String url = CommonUtils.isRequestSuccess(urlR) ? urlR.getData() : "";
            url = url + "/after-service/detail/" + pushmsg.getShouhouId();
            Boolean isZhongyou = CommenUtil.isCheckTrue(pushmsg.getIsZhongyou())
                    || StringUtils.isNotEmpty(areaInfo.getPrintName()) && areaInfo.getPrintName().contains("中邮");
            Shouhou shouhou = this.getById(pushmsg.getShouhouId());
            //软件单接件时间在1分钟内 只推送接件信息
            if (Stream.of(1, 44).noneMatch(msgId -> Objects.equals(pushmsg.getMsgId(), msgId))
                    && shouhou != null
                    && Boolean.TRUE.equals(shouhou.getIssoft())
                    && Duration.between(Optional.ofNullable(shouhou.getModidate()).orElse(LocalDateTime.now()), LocalDateTime.now()).toMinutes() <= 1) {
                return R.error("软件单接件时间在1分钟内的不推送消息");
            }
            AreaInfo shouhouAreaInfo = null;
            if (shouhou != null && shouhou.getAreaid() != null && shouhou.getAreaid() != 0) {
                R<AreaInfo> areaInfoRet = areaInfoClient.getAreaInfoById(shouhou.getAreaid());
                if (ResultCode.SUCCESS == areaInfoRet.getCode() && areaInfoRet.getData() != null) {
                    shouhouAreaInfo = areaInfoRet.getData();
                }
            }
            if ((wxsendmsg.contains("<TEL>") || wxsendmsg.contains("{TEL}")) && shouhouAreaInfo != null) {
                wxsendmsg = wxsendmsg.replace("<TEL>", shouhouAreaInfo.getCompanyTel1())
                        .replace("{TEL}", shouhouAreaInfo.getCompanyTel1());
            }
            if (wxsendmsg.contains("<QDTEL>") || wxsendmsg.contains("{QDTEL}") || wxsendmsg.contains("<QDNAME>") || wxsendmsg.contains("{QDNAME}")) {
                String qudaoTel = "";
                String qudaoName = "";
                ShouhouQudaoTelBo shouhouQudaoTelBo = shouhouQudaoService.getShouhouQudaoTel(pushmsg.getShouhouId());
                if (shouhouQudaoTelBo != null) {
                    qudaoTel = shouhouQudaoTelBo.getTel();
                    qudaoName = shouhouQudaoTelBo.getCompany();
                }
                if (StringUtils.isEmpty(qudaoTel) && shouhouAreaInfo != null) {
                    qudaoTel = shouhouAreaInfo.getCompanyTel1();
                    qudaoName = shouhouAreaInfo.getPrintName();
                }
                wxsendmsg = wxsendmsg.replace("<QDTEL>", qudaoTel)
                        .replace("{QDTEL}", qudaoTel)
                        .replace("<QDNAME>", qudaoName)
                        .replace("{QDNAME}", qudaoName);
            }
            //记录日志
            //这两个模板为维修方案备注
            Integer type = pushmsg.getLogType();
            if (isRecordLog) {
                boolean isweb = isExcludeXtenant || !Boolean.TRUE.equals(shouhouMsgconfig.getIsEnable()) ? false : shouhouMsgconfig.getIsWebShow();
                if(wxsendmsg.contains("备用机使用过程中如有问题可及时与我们联系")){
                    isweb =true;
                }
                this.saveShouhouLog(pushmsg.getShouhouId(), wxsendmsg, StringUtils.isEmpty(pushmsg.getOptUser()) ? oaUserBO.getUserName() : pushmsg.getOptUser(), type,
                        isweb,pushmsg.getTemplateId());
            }
            //【消息不启用】和【不推送消息只记录日志】 和 [排除子租户]情况只做记录
            if (ShouhouMsgPushTypeEnum.BTSXX_ZJLRZ.getCode().equals(shouhouMsgconfig.getPushtype())
                    || !Boolean.TRUE.equals(shouhouMsgconfig.getIsEnable()) || pushmsg.getUserId().equals(76783) || isExcludeXtenant) {
                String wxsendmsgOld = wxsendmsg;
                if (wxsendmsg.contains("您的售后业务已受理，感谢您选择")) {
                    String shouhouUrl = SpringUtil.getBean(CommonService.class).getUrlByXtenant(xtenant, ExtenAntUrlTypeEnum.MURL.getCode());
                    shouhouUrl = shouhouUrl + "/after-service/detail/" + pushmsg.getShouhouId();
                    String shorturl = smsService.getShortUrl(xtenant, shouhouUrl, "");
                    wxsendmsg = StrUtil.format("您的售后业务已受理（售后单{}），为方便您了解处理进度，您可关注我司公众号查看售后处理进度，也可点击查看{}",pushmsg.getShouhouId(), shorturl);
                }
                //站内通知
                if (!isZhongyou && !pushmsg.getUserId().equals(76783) && shouhouMsgconfig.getIsNotify()) {
                    ZnSendConnBo znSendConnBo = new ZnSendConnBo();
                    znSendConnBo.setKind(2);
                    znSendConnBo.setSmsnumber(pushmsg.getUserId().toString());
                    znSendConnBo.setTitle(shouhouMsgconfig.getMsgname());
                    znSendConnBo.setContent(wxsendmsg);
                    znSendConnBo.setLink(url);
                    smsService.sendZnMsg(znSendConnBo);
                }
                wxsendmsg = wxsendmsgOld;
            } else {
                //中油推送
                if (isZhongyou && pushmsg.getIsSnedZhongyou()) {
                    String zy_url = "http://fix.999buy.com/orderDetail/" + pushmsg.getShouhouId() + "/1";
                    smsService.sendZyWeixinMsg(pushmsg.getShouhouId(), pushmsg.getUserId(), "售后维修进度提醒", shouhou.getName(), wxsendmsg, url, (shouhouAreaInfo != null && shouhouAreaInfo.getCityId() != null) ? shouhouAreaInfo.getCityId() : 0);
                }
                if (areaInfo != null && areaInfo.getIsSend()) {
                    WeixinUser weixinUser = weixinUserService.getWxxinUserByUserId(pushmsg.getUserId());
                    if (weixinUser != null && StringUtils.isNotEmpty(weixinUser.getOpenid()) && CommenUtil.isCheckTrue(weixinUser.getFollow())) {
                        //服务出险，使用微信服务模板消息推送
                        if (4 == pushmsg.getMsgId() || 5 == pushmsg.getMsgId()) {
                            String serviceName = (pushmsg.getTmpData() != null && pushmsg.getTmpData().containsKey("fuwuname")) ? pushmsg.getTmpData().get("fuwuname") : "";
                            String remark = (pushmsg.getTmpData() != null && pushmsg.getTmpData().containsKey("remark")) ? pushmsg.getTmpData().get("remark") : "";
                            // 因为模板被封了  所以暂时注释
                            // shouhouMsgService.servicesCompleteNotice(weixinUser.getOpenid(), url, wxsendmsg, pushmsg.getShouhouId().toString(), serviceName, LocalDateTime.now(), remark, weixinUser.getWxid(), xtenant, areaInfo.getCityId());
                        } else {
                            Boolean flag = shouhouMsgService.sendShouHouNotify(weixinUser.getOpenid(), url, wxsendmsg, "", shouhouMsgconfig.getFuwutxt(), shouhouMsgconfig.getChulitxt(), LocalDateTime.now(), shouhouMsgconfig.getJindutxt(), weixinUser.getWxid(), xtenant, areaInfo.getCityId());
                            if (!flag) {

                                smsService.sendSms(shouhou.getMobile(), wxsendmsg, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(pushmsg.getAreaId(), ESmsChannelTypeEnum.YZMTD));
                            }
                        }
                    } else if (ShouhouMsgPushTypeEnum.YWXTWX_WWXTDX.getCode().equals(shouhouMsgconfig.getPushtype()) && StringUtils.isNotEmpty(shouhou.getMobile())) {
                        smsService.sendSms(shouhou.getMobile(), wxsendmsg, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(pushmsg.getAreaId(), ESmsChannelTypeEnum.YZMTD));
                    }
                }
            }

            if (!shouhouMsgconfig.getIssysmsg()) {
                shouhou.setResultDtime(LocalDateTime.now());
                baseMapper.updateById(shouhou);
            }
            //记录模板推送，以及超时时长
            ShouhouMsgRecordBo shouhouMsgRecordBo = new ShouhouMsgRecordBo();
            shouhouMsgRecordBo.setMsgId(pushmsg.getMsgId());
            shouhouMsgRecordBo.setShouhouId(pushmsg.getShouhouId());
            shouhouMsgRecordBo.setInuser(pushmsg.getOptUser());
            shouhouMsgRecordBo.setMsgContent(wxsendmsg);
            shouhouMsgRecordBo.setPushtime(LocalDateTime.now());
            shouhouMsgRecordBo.setTimeOut(0);
            shouhouMsgService.addShoushouMsgRecord(shouhouMsgRecordBo, shouhouMsgconfig.getTimeoutHour());
            //处理方案，取机通知
            if (pushmsg.getMsgId() == 2 || pushmsg.getMsgId() == 3
                    || pushmsg.getMsgId() == 29 || pushmsg.getMsgId() == 30 || pushmsg.getMsgId() == 31) {
                if (pushmsg.getMsgId() == 2 || pushmsg.getMsgId() == 3) {
                    Duration d = Duration.between(shouhou.getModidate(), LocalDateTime.now());
                    Integer timeout = (int) d.toMinutes();
                    if (timeout >= 0) {
                        ShouhouTimer shouhouTimer = new ShouhouTimer();
                        shouhouTimer.setShouhouid(pushmsg.getShouhouId());
                        shouhouTimer.setChuli(timeout);
                        shouhouTimerService.addShouhouTimer(shouhouTimer);
                    }
                } else {
                    shouhou.setQujitongzhitime(LocalDateTime.now());
                    baseMapper.updateById(shouhou);
                    List<ShouhouTestResultBo> shouhouTestResultBoList = shouhoutestInfoService.checkShouhouTestResult(Arrays.asList(pushmsg.getShouhouId()));
                    if (CollectionUtils.isNotEmpty(shouhouTestResultBoList)) {
                        ShouhouTestResultBo shouhouTestResultBo = shouhouTestResultBoList.get(0);
                        if (shouhouTestResultBo.getDtime() != null) {
                            Duration d = Duration.between(shouhouTestResultBo.getDtime(), LocalDateTime.now());
                            Integer timeout = (int) d.toMinutes();
                            if (timeout >= 0) {
                                ShouhouTimer shouhouTimer = new ShouhouTimer();
                                shouhouTimer.setShouhouid(pushmsg.getShouhouId());
                                shouhouTimer.setQuji(timeout);
                                shouhouTimerService.addShouhouTimer(shouhouTimer);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("售后消息推送异常：{}", e.getMessage());
            String message = "售后推送通知异常,模板(" + pushmsg.getMsgId() + ")：" + e.getMessage() + ";DATA:" + JSON.toJSONString(pushmsg);
            weixinUserService.senWeixinAndOaMsg(message, message, "", constantsSource.getExsendmsgUserids(), OaMesTypeEnum.YCTZ.getCode().toString());
        }
        return R.error("售后消息推送失败");
    }

    @Override
    public String getReachTime(int areaId, int toAreaId, LocalDateTime timeFrom) {
        try {
            String url = wwwUrlSource.getReacheTime(String.valueOf(areaId), String.valueOf(toAreaId),
                    timeFrom == null ? "" : DateUtil.localDateTimeToString(timeFrom));
            String json = HttpClientUtil.get(url);
            JSONObject obj = JSON.parseObject(json);
            if (obj != null && Integer.parseInt(obj.get("code").toString()) == 0) {
                return obj.get("data").toString();
            }
        } catch (Exception e) {
            String sendHanderUrl = inwcfUrlSource.getOaMsg("回收单转地区获取送达时间异常:" + e.getMessage(), null,
                    constantsSource.getExsendmsgUserids(), OaMesTypeEnum.YCTZ.getCode().toString());
            HttpClientUtil.get(sendHanderUrl);
        }
        return "";
    }

    @Override
    public ShouhouAreaIdBo getShouhouAreaIdNew(Integer shouhouId) {
        Shouhou shouhou = baseMapper.selectList(new LambdaQueryWrapper<Shouhou>().eq(Shouhou::getId, shouhouId).select(Shouhou::getAreaid, Shouhou::getToareaid)).stream().findFirst().orElse(null);
        ShouhouAreaIdBo shouhouAreaIdBo = new ShouhouAreaIdBo();
        if (shouhou != null) {
            shouhouAreaIdBo.setAreaId(shouhou.getAreaid());
            shouhouAreaIdBo.setToAreaId(shouhou.getToareaid());
        }
        return shouhouAreaIdBo;
    }

    @Override
    public LocalDateTime getJiejianTime(Integer shouhouId) {
        if (shouhouId == null || shouhouId == 0) {
            return null;
        }
        return baseMapper.getJiejianTime(shouhouId);
    }

    /**
     * 获取售后日志
     *
     * @param shouhouId
     * @return
     */
    @Override
    public List<ShouhouLogBo> getShouhouLogs(Integer shouhouId) {
        Optional<ShouhouLogNew> shouhouLogNewOpt = shouhouLogNewRepository.findById(shouhouId);
        if (!shouhouLogNewOpt.isPresent()) {
            return null;
        }
        ShouhouLogNew shouhouLogNew = shouhouLogNewOpt.get();
        List<ShouhouLogNew.Conts> cons = shouhouLogNew.getCons();
        if (CollectionUtils.isEmpty(cons)) {
            return null;
        }
        List<Ch999UserVo> ch999UserVoList = userInfoClient.getCh999UserByUserNames(cons.parallelStream().map(p -> p.getInUser()).distinct().collect(Collectors.toList())).getData();
        if (ch999UserVoList == null) {
            ch999UserVoList = Collections.emptyList();
        }

        cons = cons.stream().sorted(Comparator.comparing(ShouhouLogNew.Conts::getDTime, Comparator.reverseOrder())).collect(Collectors.toList());
        String pcOaHost = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.OA_URL))
                .filter(t -> t.getCode() == ResultCode.SUCCESS)
                .map(R::getData)
                .orElseThrow(()-> new CustomizeException("获取域名出错"));
        List<ShouhouLogBo> result = cons.stream().map(e -> {
            ShouhouLogBo shouhouLogBo = new ShouhouLogBo();
            shouhouLogBo.setId(shouhouId);
            shouhouLogBo.setComment(e.getComment());
            shouhouLogBo.setDTime(DateUtil.localDateTimeToMinutesStr(e.getDTime()));
            shouhouLogBo.setDTimeSecond(DateUtil.getDateTimeAsString(e.getDTime()));
            shouhouLogBo.setInUser(e.getInUser());
            shouhouLogBo.setType(e.getType());
            shouhouLogBo.setIsweb(e.getIsWeb());
            shouhouLogBo.setTemplateId(e.getTemplateId());
//            shouhouLogBo.setDepartId(finalCh999UserVoList.stream().filter(p -> Objects.equals(e.getInUser(), p.getCh999Name())).map(x -> x.getDepartId()).findFirst().orElse(0));
            return shouhouLogBo;
        }).collect(Collectors.toList());
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        final String platform = Optional.ofNullable(request.getHeader("Platform")).orElse("");
        if (platform.contains("MOA")){
            String moaHost = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL))
                    .filter(t -> t.getCode() == ResultCode.SUCCESS)
                    .map(R::getData)
                    .orElseThrow(()-> new CustomizeException("获取M端域名出错"));
            result.forEach(item ->{
                //采购单日志处理: /productKC/caigouDetail?sub_id=替换/new/#/logistics/procurement/
                Pattern patternV2 = PatternPool.get("/productKC/caigouDetail\\?sub_id=(\\d+)");
                if(ReUtil.contains(patternV2,item.getComment())) {
                    item.setComment(ReUtil.replaceAll(item.getComment(), patternV2,StrUtil.format("{}/new/#/logistics/procurement/$1",moaHost)));
                }
                if (item.getComment().contains("换货来源维修单号：")){
                    item.setComment(item.getComment()
                            .replaceAll("/staticpc/#/after-service/order/edit/","/mshouhou/edit/").replaceAll(pcOaHost,moaHost));
                }
                if (item.getComment().contains("/addOrder/wuliu")){
                    item.setComment(item.getComment()
                            .replaceAll("/addOrder/wuliu\\?wuliuid=","/new/#/logistics/logistics-bill/").replaceAll(pcOaHost,moaHost));
                }

                if (item.getComment().contains("/staticpc/#/after-service/order/edit/")){
                    item.setComment(item.getComment()
                            .replaceAll("/staticpc/#/after-service/order/edit/","/new/#/afterService/order/detail/").replaceAll(pcOaHost,moaHost));
                }
                //乐捐单日志处理
                if (item.getComment().contains("/punish/PunishDetail?sub_id=")){
                    item.setComment(item.getComment()
                            .replaceAll("/punish/PunishDetail\\?sub_id=","/mpunish/editpunish?sub_id=").replaceAll(pcOaHost,moaHost));
                }
                //新机订单连接日志处理
                if (item.getComment().contains("/addOrder/editOrder?SubID=")){
                    item.setComment(item.getComment()
                            .replaceAll("/addOrder/editOrder\\?SubID=","/order/editOrder?SubID=").replaceAll(pcOaHost,moaHost));
                }
                //良品订单连接日志处理
                if (item.getComment().contains("/punish/PunishDetail?sub_id=")){
                    item.setComment(item.getComment()
                            .replaceAll("/StockOut/editOrder\\?SubID=","/mstockout/editorder?SubID=").replaceAll(pcOaHost,moaHost));
                }
                //预约单连接日志处理
                if (item.getComment().contains("/staticpc/#/after-service/bespeak/")){
                    item.setComment(item.getComment()
                            .replaceAll("/staticpc/#/after-service/bespeak/","/new/#/afterService/detail/").replaceAll(pcOaHost,moaHost));
                }
                Pattern pattern = PatternPool.get("\"/shouhou/edit/(\\d+)\"");
                if(ReUtil.contains(pattern,item.getComment())) {
                    item.setComment(ReUtil.replaceAll(item.getComment(), pattern,StrUtil.format("\"{}/mshouhou/edit/$1\"",moaHost)));
                }
                // /staticpc/#/evaluate/detail?evaluateId=替换/new/#/operation/customers-appraisal/detail/
                pattern = PatternPool.get("\"/staticpc/#/evaluate/detail\\?evaluateId=(\\d+)\"");
                if(ReUtil.contains(pattern,item.getComment())) {
                    item.setComment(ReUtil.replaceAll(item.getComment(), pattern,StrUtil.format("\"{}/new/#/operation/customers-appraisal/detail/$1\"",moaHost)));
                }
            });
        }else{
            OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
            // pc访问, 所有m版日志, 增加oaToken
            result.forEach(item ->{
                if (StrUtil.contains(item.getComment(), "/after-service/agreement")){
                    if (oaUser != null) {
                        String comment = item.getComment();
                        // 使用正则找到 a 标签中的 href 链接并追加 oaToken
                        String updatedComment = comment.replaceAll(
                                "(<a\\s+[^>]*href=[\"']?)([^\"'>]*)([\"'>])",
                                "$1$2" + (comment.contains("?") ? "&" : "?") + "oaToken=" + oaUser.getToken() + "$3"
                        );
                        item.setComment(updatedComment);
                    }

                }
            });
        }
        return result;
    }

    /**
     * 保存售后日志
     *
     * @param shouhouId
     * @param comment
     * @param inUser
     * @param type
     * @param isweb
     */
    @Override
    public void saveShouhouLog(Integer shouhouId, String comment, String inUser, Integer type, Boolean isweb) {
        saveShouhouLog(shouhouId,comment,inUser,type,isweb,null);
    }

    /**
     * 保存售后日志
     *
     * @param shouhouId
     * @param comment
     * @param inUser
     * @param type {@link ShouHouLogTypeEnum}
     * @param isweb
     */
    @Override
    public void saveShouhouLog(Integer shouhouId, String comment, String inUser, Integer type, Boolean isweb, Integer templateId) {

        String switchValue = sysConfigService.getValueByCode(SysConfigConstant.AFTER_LOG_SWITCH);
        if (StringUtils.isNotEmpty(switchValue) && Objects.equals(switchValue, "1")) {
            shouhouLogNewService.saveShouhouLog(shouhouId, comment, inUser, type, isweb);
            return;
        }

        if (StringUtils.isEmpty(comment) || shouhouId == null || shouhouId == 0) {
            return;
        }
        if (isweb == null) {
            isweb = false;
        }
        Optional<ShouhouLogNew> shouhouLogNewOpt = shouhouLogNewRepository.findById(shouhouId);
        ShouhouLogNew shouhouLogNew = null;
        if (shouhouLogNewOpt.isPresent()) {
            shouhouLogNew = shouhouLogNewOpt.get();
        } else {
            shouhouLogNew = new ShouhouLogNew(shouhouId);
        }
        ShouhouLogNew.Conts conts = new ShouhouLogNew.Conts();
        conts.setComment(comment);
        conts.setDTime(LocalDateTime.now());
        conts.setInUser(inUser);
        conts.setIsWeb(isweb);
        conts.setType(type);
        conts.setTemplateId(templateId);
        shouhouLogNew.getCons().add(conts);
        shouhouLogNewRepository.save(shouhouLogNew);
    }

    @Override
    public void saveShouhouLog(Integer shouhouId, String comment, String inUser) {
        saveShouhouLog(shouhouId, comment, inUser, null, false);
    }

    @Override
    @Transactional
    public R<Boolean> addOtherCostPriceByPPriceid(WxFeeBo m, List<ShouHouPpidDict> ppidDictList) {

        //筛选出ppids中包含请求参数中ppid的项
        List<ShouHouPpidDict> ppidDictLists = ppidDictList.stream()
                .filter(e -> Arrays.stream(e.getPpids().split(",")).anyMatch(ppid->ppid.equals(m.getPpid().toString())))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ppidDictLists)) {
            Integer ppriceid = ppidDictLists.get(0).getPpid();
            //判断是否出过同样的配件
            Integer count = shouHouOtherCostPriceByPPriceidMapper.isOutSameFitting(m.getShouhouId(), ppriceid);
            if (count == null) {
                //获取成本和价格库存量
                List<WxCostAndKcCount> wxCostAndKcCountList = shouHouOtherCostPriceByPPriceidMapper.getCostPriceKcCount(Collections.singletonList(ppriceid), m.getAreaId());
                if (CollectionUtils.isNotEmpty(wxCostAndKcCountList)) {
                    WxCostAndKcCount wxCostAndKcCount = wxCostAndKcCountList.get(0);
                    if (wxCostAndKcCount.getLeftCount() != null && wxCostAndKcCount.getLeftCount() > 0) {
                        m.setPpid(ppriceid);
                        m.setPrice(BigDecimal.ZERO);
                        m.setInprice(wxCostAndKcCount.getInPrice() == null ? BigDecimal.ZERO : wxCostAndKcCount.getInPrice());
                        m.setUser("系统");
                        m.setPriceGs(BigDecimal.ZERO);
                        m.setPrice1(BigDecimal.ZERO);
                        m.setPid(0);
                        m.setProductName(wxCostAndKcCount.getProductName());
                        m.setKinds(2);

                        Wxkcoutput wxkcoutput = new Wxkcoutput();
                        wxkcoutput.setWxid(m.getShouhouId());
                        wxkcoutput.setName(m.getProductName());
                        wxkcoutput.setInuser(m.getUser());
                        wxkcoutput.setDtime(LocalDateTime.now());
                        wxkcoutput.setAreaid(m.getAreaId());
                        wxkcoutput.setPrice(m.getPrice());
                        wxkcoutput.setPrice1(m.getPrice1());
                        wxkcoutput.setPriceGs(m.getPriceGs());
                        wxkcoutput.setInprice(m.getInprice());
                        wxkcoutput.setPpriceid(m.getPpid());
                        wxkcoutput.setStats(0);
                        wxkcoutput.setOutputDtime(LocalDateTime.now());
                        wxkcoutput.setIslockkc(false);

                        wxkcoutputService.save(wxkcoutput);
                        if (m.getKinds().equals(2)) {
//                            SmallproNormalCodeMessageRes codeMessageRes = smallproForwardExService.stockOperations(m.getPpid(), -1, m.getPrice(), m.getAreaId(), m.getUser(), "", "维修自动出库,售后单:" + m.getShouhouId(), null, 0, 0, wxkcoutput.getId(), false, false);

                            OperateProductKcPara para = new OperateProductKcPara();
                            para.setPpid(m.getPpid());
                            para.setCount(-1);
                            para.setInprice(m.getPrice());
                            para.setAreaId(m.getAreaId());
                            para.setInuser(m.getUser());
                            para.setInsource("");
                            para.setComment("维修自动出库,售后单：" + m.getShouhouId());
                            para.setBasketId(null);
                            para.setCheck1(false);
                            para.setCheck2(false);
                            para.setShouhouId(Long.valueOf(wxkcoutput.getId()));
                            para.setIsLp(false);
                            para.setDiaoboFlag(false);

                            R<OperateProductKcRes> productKcR = productKcService.operateProductKc(para);

                            if (ResultCode.SUCCESS != productKcR.getCode()) {
                                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                                return R.error("操作库存失败【" + productKcR.getUserMsg() + "】");
                            }
                        }

                        //更新售后费用信息
                        Shouhou sh = super.getById(m.getShouhouId());
                        BigDecimal costPrice = sh.getCostprice() == null ? BigDecimal.ZERO : sh.getCostprice();
                        BigDecimal feiYong = sh.getFeiyong() == null ? BigDecimal.ZERO : sh.getFeiyong();
                        costPrice = costPrice.add(m.getInprice() == null ? BigDecimal.ZERO : m.getInprice());
                        feiYong = feiYong.add(m.getPrice() == null ? BigDecimal.ZERO : m.getPrice()).add(m.getPriceGs() == null ? BigDecimal.ZERO : m.getPriceGs());
                        boolean isRuncmd = super.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getCostprice, costPrice).set(Shouhou::getFeiyong, feiYong).eq(Shouhou::getId, m.getShouhouId()));

                        if (!isRuncmd) {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return R.error("更新售后费用信息失败");
                        }

                    }
                }

            }
        }
        return R.success("处理成功", true);
    }

    @Override
    public R<Boolean> setProcessConfirmLog(Integer shouhouId, Integer type) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        String printName = sysConfigService.getWebNameByXtenant(oaUserBO.getXTenant());
        String message1 = "您的设备已经到达" + printName + "售后中心。";
        String message2 = "尊敬的" + printName + "会员，您的产品售后问题需送往相应品牌行货售后处理，我们将在48小时内为您提供维修方案，很抱歉给您带来不便。请您保持电话畅通，我们会尽快与您取得联系！";
        Shouhou shouhou = this.getById(shouhouId);
        if (shouhou == null) {
            return R.error("操作失败：售后单不存在");
        }
        List<String> stats = null;
        if (StringUtils.isNotEmpty(shouhou.getProcessConfirmStats())) {
            String statsArr[] = shouhou.getProcessConfirmStats().split(",");
            stats = Arrays.stream(statsArr).filter(e -> StringUtils.isNotEmpty(e)).collect(Collectors.toList());
        } else {
            stats = new ArrayList<>();
        }
        if (CollectionUtils.isNotEmpty(stats) && stats.contains(String.valueOf(type))) {
            return R.error("操作失败：此单据已经确认过");
        }
        stats.add(String.valueOf(type));
        String content = "";
        switch (type) {
            case 1:
                content = message1;
                break;
            case 2:
                content = message2;
                break;
        }
        this.saveShouhouLog(shouhouId, content, oaUserBO.getUserName(), null, type == 2 ? true : false);
        shouhou.setProcessConfirmStats(String.join(",", stats));
        this.updateById(shouhou);
        if (1 == type) {
            shouhouTimePointService.saveShouhouTimePoint(shouhouId, ShouhouTimePointTypeEnum.JCZQR.getCode(), LocalDateTime.now());
        }
        return R.success("处理成功", true);
    }

    @Override
    public R<Boolean> updateTelTime(Integer shouhouId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        String comment = "* 客服人员与您电话联系";
        this.saveShouhouLog(shouhouId, comment, oaUserBO.getUserName(), null, true);
        UpdateWrapper<Shouhou> up = new UpdateWrapper<>();
        up.lambda().set(Shouhou::getTeltime, LocalDateTime.now()).set(Shouhou::getResultDtime, LocalDateTime.now()).eq(Shouhou::getId, shouhouId);
        Boolean b = this.update(up);
        if (b) {
            return R.success("处理成功", true);
        }
        return R.error("处理失败");
    }

    @Override
    public R<Boolean> addPqDan(Integer shouhouId, String orderId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        R<Boolean> ret = tApplyinfoService.validtPiqian(orderId);
        if (ResultCode.SUCCESS != ret.getCode()) {
            return ret;
        }
        String result = "批签单：<a href='/office/Apply/Detail.aspx?id=" + orderId + "' target=_blank>" + orderId + "</a>   ";
        this.saveShouhouLog(shouhouId, result, oaUserBO.getUserName(), 0, false);
        return R.success("请求成功", true);
    }


    @Override
    public ShouhouFeiyongBo getShouhouFeiyong(Integer wxId, Boolean shouhouServicesFlag) {
        return baseMapper.getShouhouFeiyong(wxId, shouhouServicesFlag);
    }

    @Override
    public Integer updateFeiYongByPriceAndId(BigDecimal price, Integer shouhouId) {
        return baseMapper.updateFeiYongByPriceAndId(price, shouhouId);
    }

    @Override
    public BigDecimal getWeiXinHongbao(Integer basketId, String imei) {
        if ((basketId == null || basketId == 0) && StringUtils.isNotEmpty(imei)) {
            basketId = baseMapper.getBasketIdByImei(imei);
        }
        if (basketId != null && basketId != 0) {
            BigDecimal grandAmount = baseMapper.getGrandAmount(basketId);
            return grandAmount;
        }
        return BigDecimal.ZERO;
    }

    @Override
    public List<ShouhouDaoJiShiBo> getShouhouTimeByPid(Integer ppriceid) {
        return baseMapper.getShouhouTimeByPid(ppriceid);
    }

    @Override
    public R<Boolean> addShouHouLog(ShouhouLogReq req) {
        if (StringUtils.isEmpty(req.getContent())) {
            return R.error("请填写备注");
        }

        if (req.getIsSendZY() == null) {
            req.setIsSendZY(0);
        }
        if (req.getMessageTplId() == null) {
            req.setMessageTplId(0);
        }

        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.error("请登陆后操作");
        }
        List<ShouhouLogBo> shLogs = shouhouLogsService.getListByShouHouIdAndOrder(req.getWxId(), 0);
        ShouhouLogBo log = null;
        if (CollectionUtils.isNotEmpty(shLogs)) {
            Optional<ShouhouLogBo> optional = shLogs.stream().filter(e -> e.getComment().indexOf(req.getContent()) != -1 && LocalDateTime.now().getMinute() - DateUtil.stringToLocalDateTimeWithMinutesFormat(e.getDTime()).getMinute() < 60).findFirst();
            if (optional.isPresent()) {
                log = optional.get();
            }
        }
        if (log == null) {
            shouhouLogsService.addShouhouLog(oaUserBO.getUserName(), req.getWxId(), req.getType(), req.getContent(), req.getNotice(), req.getIsSendZY() > 0, req.getMessageTplId(), req.getIsWeb());
        } else {
            return R.error("请勿频繁提交重复内容的处理进程");
        }
        return R.success(true);
    }

    @Override
    public Boolean isKunMingShi(Integer areaId) {
        Boolean flag = false;
        R<List<AreaInfo>> areaInfoR = areaInfoClient.listAll();
        if (areaInfoR.getCode() == ResultCode.SUCCESS && CollectionUtils.isNotEmpty(areaInfoR.getData())) {
            Optional<AreaInfo> optional = areaInfoR.getData().stream().filter(e -> e.getId().equals(areaId)).findFirst();
            if (optional.isPresent()) {
                return Arrays.asList(530102, 530103, 530111, 530112).contains(optional.get().getCityId());
            }
        }
        return flag;
    }

    @Override
    public ShouhouBo getOne(Integer shouhouId) {
        List<ShouhouBo> shouhouBoList = baseMapper.getOne(shouhouId);
        if (CollectionUtils.isNotEmpty(shouhouBoList)) {
            return shouhouBoList.get(0);
        }
        return null;
    }

    @Override
    public Integer getAreaIdByShouhouId(Integer shouhouId) {
        return baseMapper.getAreaIdByShouhouId(shouhouId);
    }

    @Override
    public ShouHouImportantBo isSubCollectZdb(Integer shouhouId) {
        return baseMapper.isSubCollectZdb(shouhouId);
    }

    @Override
    public boolean isSubCollect(Integer subId, Integer ch999Id, Integer collectType) {
        Integer res = baseMapper.isSubCollect(subId, ch999Id, collectType);
        return (res != null && res > 0);
    }

    @Override
    public List<String> searchSubCollect(Integer subId, Integer collectType) {
        List<String> list = baseMapper.searchSubCollect(subId, collectType);
        return list;
    }

    @Override
    public R<String> subCollectDeal(Integer subId, Integer type, Integer subCollectKind) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }
        if (subId == null) {
            return R.error("传入单号无效");
        }
        type = (type == null ? 0 : type);
        subCollectKind = (subCollectKind == null ? SubCollectTypeEnum.SUB.getCode() : subCollectKind);
        List<SubCollection> subCollectionList = subCollectionService.list(new QueryWrapper<SubCollection>().lambda()
                .eq(SubCollection::getSubId, subId)
                .eq(SubCollection::getKind, subCollectKind)
                .eq(SubCollection::getCh999Id, oaUserBO.getUserId()));
        //取消
        if (type == 1) {
            if (CollectionUtils.isNotEmpty(subCollectionList)) {
                subCollectionService.removeById(subCollectionList.get(0).getId());
                return R.success("取消成功！");
            } else {
                return R.error("取消失败，未查询到关注信息！");
            }
        }

        //关注
        if (CollectionUtils.isEmpty(subCollectionList)) {
            SubCollection subCollection = new SubCollection();
            subCollection.setSubId(subId.longValue());
            subCollection.setCh999Id(oaUserBO.getUserId());
            subCollection.setCh999Name(oaUserBO.getUserName());
            subCollection.setDtime(LocalDateTime.now());
            subCollection.setKind(subCollectKind);
            subCollectionService.save(subCollection);
            return R.success("关注成功！");
        } else {
            return R.error("您已关注过该订单");
        }
    }

    @Override
    public R<String> checkReturnType(long id, int type) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Integer areaKind1 = oaUserBO.getAreaKind1();
        String ids = String.valueOf(id);
        if (type == 6 || type == 7) {
            Integer subPid = baseMapper.getSubPidBySubId(id);
            if (CommenUtil.isNotNullZero(subPid)) {
                ids = ids + "," + subPid.toString();
            }
        }
        String inuser = baseMapper.getSubInuser(id, ids, type);
        String returnType = this.getReturnTypeByPayWay(inuser, areaKind1);
        if (StringUtils.isNotEmpty(returnType)) {
            return R.success(returnType);
        }
        return R.error("找不到该退款方式");
    }

    @Override
    public String getReturnTypeByPayWay(String inuser, Integer areakind1) {
        String returnType = "";
        switch (inuser) {
            case "支付宝":
                returnType = "支付宝返回";
                break;
            case "微信":
                returnType = "微信返回";
                break;
            case "扫码枪":
                returnType = "兴业银行扫码返回";
                break;
            case "兴业银行扫码":
                returnType = "兴业银行扫码返回";
                break;
            case "ApplePay":
                returnType = "ApplePay返回";
                break;
            case "支付宝（兴业）":
                returnType = "支付宝（兴业）返回";
                break;
            case "浦发扫码":
                returnType = "银行转账";
                break;
            case "支付宝(pay1)":
                returnType = "银行转账";
                break;
            case "支付宝(dzpay)":
                returnType = "支付宝(dzpay)返回";
                break;
            case "兴业扫码(叁玖)":
                returnType = "兴业扫码(叁玖)返回";
                break;
            case "浦发扫码(92653)":
                returnType = "银行转账";
                break;
            case "浦发扫码(92427)":
                returnType = "银行转账";
                break;
            case "中信扫码(06306)":
                returnType = "银行转账";
                break;
            case "平安扫码(39878)":
                returnType = "平安扫码(39878)返回";
                break;
            case "首信易扫码支付":
                returnType = "首信易扫码支付返回";
                break;
            case "中信扫码(06305)":
                returnType = "中信扫码(06305)返回";
                break;
            case "浦发扫码(93155)":
                returnType = "浦发扫码(93155)返回";
                break;
            case "浦发扫码(93057)":
                returnType = "银行转账";
                break;
            case "中信扫码(74026)":
                returnType = "银行转账";
                break;
            case "中信扫码(25762)":
                returnType = "银行转账";
                break;
            case "兴业扫码(24833)":
                returnType = "兴业扫码(24833)返回";
                break;
            case "微信(yy)":
                returnType = "微信(yy)返回";
                break;
            case "微信APP(yy)":
                returnType = "微信APP(yy)返回";
                break;
            case "支付宝(yy)":
                returnType = "支付宝(yy)返回";
                break;
            case "微信(电子)":
                returnType = "微信(电子)返回";
                break;
            case "微信(电子)APP":
                returnType = "微信(电子)APP返回";
                break;
        }

        if ("建行分期".equals(inuser) && areakind1 == 1) {
            returnType = "建行分期返回";
        }

        return returnType;
    }

    @Override
    public R<Boolean> pandianCancel(Integer wxId) {
        Boolean flag = super.update(new UpdateWrapper<Shouhou>().lambda()
                .set(Shouhou::getPandian, 0).in(Shouhou::getId, Arrays.asList(wxId))
                .and(sh -> sh.ne(Shouhou::getPandianinuser, "已核对").or().isNull(Shouhou::getPandianinuser))
        );
        return R.success(flag);
    }

    @Override
    public R<String> yuyueCheck(Integer shouhouId) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.error("请登录后操作");
        }

        Boolean exec = this.update(new UpdateWrapper<Shouhou>().lambda()
                .set(Shouhou::getYuyueCheck, 1)
                .set(Shouhou::getInuser, oaUserBO.getUserName()).eq(Shouhou::getId, shouhouId));
        return exec ? R.success("确认完成") : R.error("确认失败");
    }

    @Override
    public R<String> sendWxPz(Integer shouhouId, Integer userId, String mobile) {
        //发送电子凭证
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        R<Boolean> pzRes = shouhouYuyueService.certificateShouhouYuyueAndShouhou(shouhouId, userId, 3, null);
        if (pzRes.getCode() == ResultCode.SUCCESS) {
            //发送短信
            String printName = sysConfigService.getWebNameByXtenant(oaUserBO.getXTenant());
            String msg = "尊敬的" + printName + "会员，您的维修单号" + shouhouId + "已为你生成电子凭证，点击查看 " + pzRes.getUserMsg();
            long xtenant = Namespaces.get();
            String openXtenantStr = apolloEntity.getSmsConfigOpenXtenant();
            if (XtenantEnum.isSaasXtenant()
                    && org.apache.commons.lang3.StringUtils.isNotEmpty(openXtenantStr)
                    && CommonUtils.covertIdStr(openXtenantStr).contains((int) xtenant)) {
                R<SmsConfigVO> smsConfigResult = smsConfigCloud.getConfigByCode(Namespaces.get(),
                        SmsConfigCodeEnum.CODE_80.getCode());
                if (com.jiuji.tc.utils.common.CommonUtils.isRequestSuccess(smsConfigResult)) {
                    SmsConfigVO smsConfig = smsConfigResult.getData();
                    // 消息内容
                    List<SmsConfigVO.SmsField> fields = smsConfig.getFields();
                    String message = smsConfig.getTemplate();
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(message)
                            && CollectionUtils.isNotEmpty(fields)) {
                        for (SmsConfigVO.SmsField field : fields) {
                            if ("<printName>".equals(field.getValue())) {
                                message = message.replace(field.getValue(), printName);
                            }
                            if ("<subId>".equals(field.getValue())) {
                                message = message.replace(field.getValue(), String.valueOf(shouhouId));
                            }
                            if ("<detailLink>".equals(field.getValue())) {
                                message = message.replace(field.getValue(), pzRes.getUserMsg());
                            }
                        }
                    }
                    // 推送方式
                    List<Integer> pushMethods = smsConfig.getPushMethod();
                    // sms消息
                    boolean sendSmsMessage = CollectionUtils.isNotEmpty(pushMethods)
                            && pushMethods.contains(SmsPushMethodEnum.SMS.getCode());
                    if (sendSmsMessage) {
                        msg = message;
                    }
                }
            }
            log.info("电子凭证内容:{}",msg);
            smsService.sendSms(mobile, msg, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(oaUserBO.getAreaId(), ESmsChannelTypeEnum.YZMTD));
        }

        return R.success("发送成功");
    }

    @Override
    public R<String> qujiSendmsg(Integer wxId, String msg) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }

        this.saveShouhouLog(wxId, msg, oaUserBO.getUserName(), null, true);

        ShouhouUserInfoBo shUserInfo = baseMapper.getShouhouUserInfo(wxId);
        if (shUserInfo == null) {
            return R.error("当前单号信息不存在");
        }

        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(shUserInfo.getAreaId());
        if (areaInfoR.getCode() != ResultCode.SUCCESS && areaInfoR.getData() == null) {
            return R.error("获取门店信息出错");
        }

        AreaInfo areaInfo = areaInfoR.getData();
        if (areaInfo.getIsSend()) {
            Integer userId = shUserInfo.getUserId();
            WeixinUser weixinUser = weixinUserService.getWxxinUserByUserId(userId);
            if (weixinUser == null) {
                return R.error("获取会员信息出错");
            }
            if (StringUtils.isNotEmpty(weixinUser.getOpenid())) {
                shouhouMsgService.sendWeixinMsg(wxId, msg, false, true);
            } else {
                smsService.sendSms(shUserInfo.getMobile(), msg, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(shUserInfo.getAreaId(), ESmsChannelTypeEnum.YZMTD));
                super.update(new UpdateWrapper<Shouhou>().lambda().set(Shouhou::getQujitongzhitime, LocalDateTime.now()).eq(Shouhou::getId, wxId));
            }
        } else {
            return R.error("当前地区不可发送通知");
        }

        return R.success("操作成功");
    }

    @Override
    public AreaInfo getAreaSubject(Integer nowArea) {
        AreaInfo areaInfo = new AreaInfo();
        if (nowArea == null || nowArea < 0) {
            return areaInfo;
        }
        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(nowArea);
        if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
            areaInfo = areaInfoR.getData();
        }
        /*else {
            areaInfo.setId(nowArea);
            areaInfo.setIsSend(true);
            areaInfo.setPrintName("九机网");
            areaInfo.setXtenant(0);
            areaInfo.setAuthorizeId(1);
            areaInfo.setLogo(moaUrlSource.getBasicUrl() + "/static/24,37835b86a5e75b");
            areaInfo.setSmsChannel(JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_JIUJI.getCode().toString());
            areaInfo.setSmsChannelMarketing(JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_JIUJI_MARKETING.getCode().toString());
            OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
            if (oaUserBO != null && oaUserBO.getXTenant() != null) {
                Integer xTenant = oaUserBO.getXTenant();
                R<String> mUrlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, xTenant);
                R<String> webUrlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.WEB_URL, xTenant);
                R<String> hsUrlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.HS_URL, xTenant);
                areaInfo.setMUrl(CommonUtils.isRequestSuccess(mUrlR) ? mUrlR.getData() : "");
                areaInfo.setWebUrl(CommonUtils.isRequestSuccess(webUrlR) ? webUrlR.getData() : "");
                areaInfo.setHsUrl(CommonUtils.isRequestSuccess(hsUrlR) ? hsUrlR.getData() : "");
            }
        }*/
        if (Optional.ofNullable(areaInfo).map(AreaInfo::getPrintName).filter("华为授权"::equals).isPresent()) {
            areaInfo.setSmsChannel("27");
        }
        return areaInfo;
    }

    @Override
    public R<String> saveZySxBusinessInfo(Integer shouhouId, String mobile, String username) {
        shouhouBusinessinfoService.remove(new UpdateWrapper<ShouhouBusinessinfo>().lambda().eq(ShouhouBusinessinfo::getShouhouid, shouhouId));

        ShouhouBusinessinfo businessinfo = new ShouhouBusinessinfo();
        businessinfo.setShouhouid(shouhouId);
        businessinfo.setMobile(mobile);
        businessinfo.setUsername(username);
        boolean flag = shouhouBusinessinfoService.save(businessinfo);
        if (flag) {
            return R.success("保存成功");
        } else {
            return R.error("保存失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> saveOrUpdateShouhouInfo(ShouhouInfoRes fm) {
        //校验数据
        R<Boolean> checkR = checksaveOrUpdateShouhouInfo(fm);
        if(!checkR.isSuccess()){
            return checkR;
        }
        ShouhouReq shouhouReq = new ShouhouReq();
//
//        if (CommenUtil.isNotNullZero(fm.getYuyueid()) && (fm.getWebstats() != null && fm.getWebstats() < 3)) {
//            return R.error("预约单生成的维修单,需要收件操作才可以进行其他操作");
//        }
        BeanUtils.copyProperties(fm, shouhouReq);
        if (fm.getId() == null) {
            R<Boolean> saveResult = this.saveShouhou(shouhouReq);
            fm.setId(shouhouReq.getId());
            cantFixReasonService.saveData(new SaveCantFixReasonVO(shouhouReq.getId(),fm.getReasonList()));
            return saveResult;
        }
        Integer fmstats = fm.getStats() == null ? 0 : fm.getStats();
        if (fm.getServiceType() != null) {
            List<Integer> serviceTypes = Arrays.asList(1, 2, 5, 6);
            if (serviceTypes.contains(fm.getServiceType()) && fm.getBaoxiu() != 0) {
                return R.error("选择用【意外保、碎屏保、延长保、进水保】服务进行维修，请修改保修状态为“不在保”才可操作。");
            }
        }

        if (this.hasJinshuibao(fm.getId(), BaoXiuTypeEnum.JSB.getCode())) {
            if (!fm.getServiceType().equals(BaoXiuTypeEnum.JSB.getCode()) || (fm.getServiceType().equals(BaoXiuTypeEnum.JSB.getCode()) && fm.getWxkind() != 2)) {
                return R.error("已提交进水保换机服务申请，若要变更处理方式请撤销进水保换机申请。");
            }
        }

        if (this.hasJinshuibao(fm.getId(), BaoXiuTypeEnum.YHDX.getCode())) {
            if (!fm.getServiceType().equals(BaoXiuTypeEnum.YHDX.getCode()) || (fm.getServiceType().equals(BaoXiuTypeEnum.YHDX.getCode()) && fm.getWxkind() != 2)) {

                String errMsg = "已提交" + EnumUtil.getMessageByCode(BaoXiuTypeEnum.class, BaoXiuTypeEnum.YHDX.getCode()) + "换机服务申请，若要变更处理方式请撤销" + EnumUtil.getMessageByCode(BaoXiuTypeEnum.class, BaoXiuTypeEnum.YHDX.getCode()) + "服务换机申请。";
                return R.error(errMsg);
            }
        }
        ShouhouWrapper wrapper = new ShouhouWrapper();
        ShouhouInfoReq req = wrapper.buildShouhouReq(fm);

        R<Boolean> updateR;
        try {
            updateR = SpringUtil.getBean(ShouhouService.class).updateShouhouInfo(req);
        } catch (CustomizeException e) {
            //客户异常提示返回给前端
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            String errorMsg = e.getMessage();
            if(e.getCause() != null){
                errorMsg = StrUtil.format("{},错误编号：{}",errorMsg, CommonUtils.getRandom4Code());
                log.error(errorMsg, e);
            }
            return R.error(errorMsg);
        } catch (Exception e) {
            AtomicReference<String> msgRef = new AtomicReference<>("");
            RRExceptionHandler.logError("售后单保存更新",fm,e,msgRef::set);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error(msgRef.get());
        }
        if (updateR.getCode() == ResultCode.SUCCESS) {
            shouhouExService.updateShouhouTroubles(fm.getId(), req.getTroubleIds());
            //三方订单状态同步
            thirdPlatStatusSyncService.thirdShouhouStatusSync(ShouhouOrderTypeEnum.SHOUHOU.getCode(), fm.getId());
            return updateR.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
        } else {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            R<Boolean> error = R.error(updateR.getUserMsg());
            return error.addAllBusinessLog(updateR.businessLogs())
                    .addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
        }
    }

    private R<Boolean> checksaveOrUpdateShouhouInfo(ShouhouInfoRes fm) {
        try {
            Assert.isFalse(ObjectUtil.isEmpty(fm),"售后信息不能为空,保存失败");
            // 15天无理由退货
            Assert.isFalse(StrUtil.endWith(fm.getWuliyou(),ShouhouExService.WU_LI_YOU_TEXT) && ObjectUtil.isNotNull(fm.getProductId())
                    && !isNoReason(Collections.singletonList(Convert.toInt(fm.getProductId()))),"该商品不支持{}{}，详情请见商品详情页"
                    ,ShouhouExService.getWuLiYouDays(),ShouhouExService.WU_LI_YOU_TEXT);
            if (XtenantEnum.isJiujiXtenant() && SpringUtil.getBean(ShouhouRiskNotificationService.class).isSignShouhouRiskNotification(fm.getId())) {
                return R.error("已为客户发送风险告知书，请确认风险告知书后再进行操作");
            }
            //校验回收属性是否正确
            if(ObjectUtil.defaultIfNull(fm.getIshuishou(), 0) == 0
                    && ObjectUtil.defaultIfNull(fm.getSubId(), 0) >0
                    && CommenUtil.autoQueryHist(() -> SpringUtil.getBean(SubService.class).lambdaQuery()
                    .eq(Sub::getSubId, fm.getSubId()).count(), MTableInfoEnum.SUB, fm.getSubId()) <=0
            ){
                RRExceptionHandler.logError("接件设备来源错误, 商品非新机 维修单："+fm.getId(), fm, null, smsService::sendOaMsgTo9JiMan);
            }
        } catch (IllegalArgumentException e) {
            throw new CustomizeException(e.getMessage());
        }
        return R.success(null);
    }

    @Override
    public Boolean hasJinshuibao(Integer shouhouId, Integer serviceType) {
        return baseMapper.hasJinshuibao(shouhouId, serviceType) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> updateShouhouInfo(ShouhouInfoReq fm) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }
        Integer id = fm.getId();
        Integer repair = fm.getRepairLevel();
        ShouhouDtBo sh = baseMapper.getShouhouDtById(id);
        Integer mStats = -1;
        List<ShouhouMsgPushMessageBo> pusheMessageList = new LinkedList<>();
//        if (sh.getIsBakData() != null && sh.getIsBakData() == 1) {
//            return R.error("请先备份数据，再进行其他操作！");
//        }
        //只要是状态变更 且被修改到 修好或者修不好的时候就去校验 是否绑定sn
        if(XtenantEnum.isJiujiXtenant() && !WxStatusEnum.CLZ.getCode().equals(fm.getStats()) && !sh.getStats().equals(fm.getStats())){
            List<HexiaoBo> hexiaoBoList = wxkcoutputService.getHexiao(sh.getId());
            HexiaoBo hexiaoBoNoSn = hexiaoBoList.stream()
                    .filter(hexiaoBo -> Boolean.TRUE.equals(hexiaoBo.getIsSn()) && StringUtils.isEmpty(hexiaoBo.getSn()))
                    .findFirst().orElse(null);
            if(ObjectUtil.isNotNull(hexiaoBoNoSn)){
                return R.error(String.format("请先绑定 维修配件：%s  SN码", hexiaoBoNoSn.getWxpjName()));
            }
        }
        if (Objects.equals(fm.getStats(), 1) && !sh.getStats().equals(1)) {
            //改为已修好 校验是否已经进行测试
            if (XtenantEnum.isJiujiXtenant()
                    && Optional.ofNullable(fm.getId()).orElse(0) > shouhouTestInfoConfig.getNewTestShouhouId()) {
                ShouhouTestResultInfo lastTestResult = SpringUtil.getBean(ShouhouTestResultInfoService.class).getLastResultByTypeAndShouhouId(ShouhouTestTypeEnum.AFTER_REPAIRING.getCode(), fm.getId());
                if (!BbsxpUserIdConstants.XIAN_HUO.equals(Convert.toInt(fm.getUserid(), 0))
                        && (Objects.isNull(lastTestResult) || Boolean.FALSE.equals(lastTestResult.getTestResult()))
                        && !IshuishouEnum.VALUE_ADDED_RECYCLING.getCode().equals(sh.getIshuishou())) {
                    return R.error("未进行“修后测试”或“测试不通过”，请“修后测试”通过之后在进行操作");
                }
            } else {
                Integer count = shouhoutestInfoService.count(new LambdaQueryWrapper<ShouhoutestInfo>().eq(ShouhoutestInfo::getShouhouId, fm.getId()));
                if (CommenUtil.isNullOrZero(count)) {
                    return R.error("只有测试通过（或无法测试）才能变更为已修好状态");
                }
            }
            // 需要先确认保修状态
            if(BaoxiuStatusEnum.DJC.getCode().equals(fm.getBaoxiu())
                    || fm.getBaoxiu() == null && BaoxiuStatusEnum.DJC.getCode().equals(sh.getBaoxiu())){
                return R.error("请先确认保修状态, 才能变更为已修好状态");
            }

            //需要上传维修前后内部附件才可以改为已修好

            Integer count = attachmentsService.count(new LambdaQueryWrapper<Attachments>().eq(Attachments::getLinkedID, fm.getId())
                    .eq(Attachments::getType, 6).eq(Attachments::getKind, 1).in(Attachments::getKind1, Arrays.asList(8, 9)));
            if (CommenUtil.isNullOrZero(count)) {
                //todo 这里等上线后再放开限制
//                return R.error("温馨提示：检测到您的维修单未上传：拆机前设备内部照片以及维修完成内部照片，请尽快上传！");
            }
        }
        List<ShouhouLogNewBo> shLogs = new LinkedList<>();
        LambdaUpdateWrapper<Shouhou> updateWrapper = new LambdaUpdateWrapper<>();
        try {
            //判断如果过是现货维修 并且是九机
            if(BbsxpUserIdConstants.XIAN_HUO==(Optional.ofNullable(fm.getUserid()).orElse(0L).intValue())
                    && XtenantEnum.isJiujiXtenant()){
                updateWrapper.set(Shouhou::getFaultType, fm.getFaultType());
                Optional.ofNullable(fm.getId()).ifPresent(item->{
                    Integer faultType = sh.getFaultType();
                    String faultTypeValueOld = FaultTypeEnum.getMessageByCode(faultType);
                    String faultTypeValueNew = FaultTypeEnum.getMessageByCode(fm.getFaultType());
                    if(!faultTypeValueOld.equals(faultTypeValueNew)){
                        String comment = String.format("故障类型：由%s 修改为%s", faultTypeValueOld, faultTypeValueNew);
                        //写维修单日志
                        this.saveShouhouLog(item, comment, oaUserBO.getUserName(), null, true);
                    }
                });
            }
        } catch (Exception e){
            RRExceptionHandler.logError("现货维修单修改故障类型日志记录异常",fm.getId(),e,smsService::sendOaMsgTo9JiMan);
        }


        if (sh.getIsQuJiE() == 0) {
            if (fm.getStats() != null) {
                mStats = sh.getStats() == null ? -1 : sh.getStats();
                if (fm.getStats().equals(1) && mStats != 1) {
                    //如果是预约维修，必须是已收货后才能改为已修好
                    if (sh.getWebstats() != null && sh.getWebstats() != 3) {
                        return R.error("没有收货操作，不可改为已修好！");
                    }
                    updateWrapper.set(Shouhou::getModidtime, LocalDateTime.now());
                    updateWrapper.set(Shouhou::getIsweixiu, 1);
                    if (sh.getWeixiudtime() == null) {
                        updateWrapper.set(Shouhou::getWeixiudtime, LocalDateTime.now());
                    }
                }
                if (fm.getStats().equals(3) && !mStats.equals(3)) {
                    updateWrapper.set(Shouhou::getIsweixiu, 0);
                    updateWrapper.set(Shouhou::getWeixiudtime, LocalDateTime.now());
                }
                if (fm.getStats().equals(0) && !mStats.equals(0)) {
                    if (sh.getIsweixiu() != null) {
                        updateWrapper.set(Shouhou::getIsweixiu, null);
                        updateWrapper.set(Shouhou::getWeixiudtime, null);
                    }
                }

                if (fm.getStats() != null && !fm.getStats().equals(sh.getStats())) {
                    String comment = "";
                    if (fm.getStats() == 1) {
                        comment = "您的设备已经处理完成，正在进行全面测试。";
                        //针对退换设备维修单，改已修好状态取消可视进程的推送
                        if (shouhouTuihuanService.count(new LambdaQueryWrapper<ShouhouTuihuan>().eq(ShouhouTuihuan::getShouhouId, id)) > 0) {
                            comment = "";
                        }
                        ShouhouQudao qudao = shouhouQudaoService.getShouhouQudaoByShouhouId(id);
                        if (qudao != null && StringUtils.isNotEmpty(qudao.getShqd2name())) {
                            new ShouhouLogNewBo(id, "您的设备已从【" + qudao.getShqd2name() + "】取回，即将进行测试。", oaUserBO.getUserName(), null, true);
                            shLogs.add(new ShouhouLogNewBo(id, "您的设备已从【" + qudao.getShqd2name() + "】取回，即将进行测试。", oaUserBO.getUserName(), null, true));
                        }
                    } else {
                        comment = "【" + EnumUtil.getMessageByCode(DealStatsEnum.class, fm.getStats()) + "】";
                    }
                    shLogs.add(new ShouhouLogNewBo(id, comment, oaUserBO.getUserName(), null, fm.getStats() == 1));
                    updateWrapper.set(Shouhou::getStats, fm.getStats());
                    updateWrapper.set(Shouhou::getWxAreaid, oaUserBO.getArea1id());
                    //移除售后单时间节点
                    shouhouTimePointService.removeShouhouTimePoint(id, Arrays.asList(ShouhouTimePointTypeEnum.YXHJCS.getCode(), ShouhouTimePointTypeEnum.XBH.getCode()));
                    if (fm.getStats() == 1) {
                        //已修好暂时不做消息推送
//                        ShouhouMsgPushMessageBo message = new ShouhouMsgPushMessageBo();
//                        message.setMsgId(23);
//                        message.setShouhouId(id);
//                        message.setAreaId(sh.getAreaid());
//                        message.setUserId(sh.getUserid() == null ? 0 : sh.getUserid().intValue());
//                        message.setOptUser(oaUserBO.getUserName());
//                        pusheMessageList.add(message);

                        //记录已修好时间
                        shouhouTimePointService.saveShouhouTimePoint(id, ShouhouTimePointTypeEnum.YXHJCS.getCode(), LocalDateTime.now());
                    } else if (fm.getStats() != null && fm.getStats() == 3) {
                        shouhouTimePointService.saveShouhouTimePoint(id, ShouhouTimePointTypeEnum.XBH.getCode(), LocalDateTime.now());
                    }
                    String sms = "订单状态由【" + EnumUtil.getMessageByCode(DealStatsEnum.class, sh.getStats()) +
                            "】更改为 【" + EnumUtil.getMessageByCode(DealStatsEnum.class, fm.getStats()) + "】";
                    shouhouMsgService.sendCollectMsg(id, sms, oaUserBO.getUserName());
                }
                if (fm.getStats() == 1 && StringUtils.isEmpty(sh.getTestuser()) && StringUtils.isNotEmpty(fm.getTestuser())) {
                    updateWrapper.set(Shouhou::getTestuser, fm.getTestuser()).set(Shouhou::getTesttime, LocalDateTime.now());
                }
            }
            if (fm.getMobile().trim().length() > 11) {
                fm.setMobile(fm.getMobile().substring(0, 11));
            }
            if (fm.getTel().trim().length() > 12) {
                fm.setTel(fm.getTel().substring(0, 12));
            }

            if (fm.getMobile().length() > 2) {
                updateWrapper.set(Shouhou::getMobile, fm.getMobile());
                if (StringUtils.isNotEmpty(sh.getMobile()) && !sh.getMobile().equals(fm.getMobile())) {
                    shLogs.add(new ShouhouLogNewBo(id, "手机由" + sh.getMobile() + "修改为" + fm.getMobile(), oaUserBO.getUserName(), null, true));
                }
            }
            if (StringUtils.isNotEmpty(fm.getTruename()) && !fm.getTruename().equals(sh.getTruename())) {
                updateWrapper.set(Shouhou::getTruename, fm.getTruename());
                if (StringUtils.isNotEmpty(fm.getTruename())) {
                    shLogs.add(new ShouhouLogNewBo(id, "姓名由" + sh.getTruename() + "修改为" + fm.getTruename(), oaUserBO.getUserName(), null, true));
                }
            }
            if (fm.getTel().length() > 2) {
                updateWrapper.set(Shouhou::getTel, fm.getTel());
            }
            if (StringUtils.isNotEmpty(fm.getProblem())) {
                updateWrapper.set(Shouhou::getProblem, fm.getProblem());
            }
            if (StringUtils.isNotEmpty(fm.getPeizhi())) {
                updateWrapper.set(Shouhou::getPeizhi, fm.getPeizhi());
            }
            updateWrapper.set(Shouhou::getWaiGuanStatus, fm.getIsWaiguan());
            if (fm.getIsWaiguan() == null || Objects.equals(Shouhou.WaiGuanStatusEnum.NO_SELECT.getCode(), fm.getIsWaiguan())) {
                updateWrapper.set(Shouhou::getWaiguan, null);
            } else{
                updateWrapper.set(Shouhou::getWaiguan, fm.getWaiguan());
            }
            if (fm.getLppeizhi() != null) {
                updateWrapper.set(Shouhou::getLppeizhi, fm.getLppeizhi());
            }
            if (fm.getRepairLevel() != null) {
                Boolean isupdateRepairLevel = true;
                if (fm.getRepairLevel().equals(2)) {
                    //芯片维修，需要出库【芯片级维修】分类下的配件才可以修改
                    Integer wxKcId = wxkcoutputService.getKcIdByWxId(id);
                    if (CommenUtil.isNullOrZero(wxKcId)) {
                        isupdateRepairLevel = false;
                    }
                }
                if (isupdateRepairLevel) {
                    if (CommenUtil.isNullOrZero(fm.getRepairLevel())) {
                        updateWrapper.set(Shouhou::getRepairLevel, 1);
                    } else {
                        updateWrapper.set(Shouhou::getRepairLevel, fm.getRepairLevel());
                    }
                }
            }

            updateWrapper.set(Shouhou::getKinds, fm.getKinds());
            String mWeixiuren = StringUtils.isEmpty(sh.getWeixiuren()) ? "" : sh.getWeixiuren();
            Boolean isRweixiuren = false;
            if (!mWeixiuren.equals(fm.getWeixiuren()) && StringUtils.isNotEmpty(fm.getWeixiuren()) && !fm.getWeixiuren().equals("深圳")) {
                if (sh.getWeixiurentime() == null) {
                    updateWrapper.set(Shouhou::getWeixiurentime, LocalDateTime.now());
                    isRweixiuren = true;
                } else {
                    LocalDateTime wxrTime = sh.getWeixiurentime();
                    isRweixiuren = sh.getReweixiuren() == null ? false : sh.getReweixiuren();
                    Duration duration = Duration.between(LocalDateTime.now(), wxrTime);
                    if (!isRweixiuren && duration.toHours() < 60) {
                        isRweixiuren = true;
                        updateWrapper.set(Shouhou::getReweixiuren, true);
                    } else {
                        isRweixiuren = false;
                    }
                }
                //维修人更改
                if (isRweixiuren || oaUserBO.getRank().contains("6d1")) {
                    updateWrapper.set(Shouhou::getWeixiuren, fm.getWeixiuren());
                    String comment = "维修人由" + sh.getWeixiuren() + "更改为" + fm.getWeixiuren();
                    this.saveShouhouLog(id, comment, oaUserBO.getUserName(), null, false);

                    shouhouMsgService.sendCollectMsg(id, "正在进行维修处理", oaUserBO.getUserName());
                    ShouhouMsgPushMessageBo message = new ShouhouMsgPushMessageBo();
                    message.setMsgId(22);
                    message.setShouhouId(fm.getId());
                    message.setAreaId(sh.getAreaid());
                    if (sh.getUserid() != null) {
                        message.setUserId(sh.getUserid().intValue());
                    }

                    Map<String, String> tmpData = new HashMap<>();
                    tmpData.put("weixiuren", fm.getWeixiuren());
                    message.setTmpData(tmpData);
                    pusheMessageList.add(message);
                }
            } else if (StringUtils.isEmpty(sh.getWeixiuren()) || (fm.getWeixiuzuid() != null && fm.getWeixiuzuid() == 0)) {
                updateWrapper.set(Shouhou::getWeixiuren, "");
            }

            if (fm.getBaoxiu() != null && !fm.getBaoxiu().equals(sh.getBaoxiu())) {
                //购买日期为空，外修机
                if (sh.getTradedate() != null || oaUserBO.getRank().contains("6c6")) {
                    //已修好的不让改
                    if (oaUserBO.getRank().contains("6c6") && sh.getStats() == 0 && fm.getStats() != 1
                            //gjzb 可以非保改为在保的状态
                            && (ObjectUtil.notEqual(fm.getBaoxiu(), BaoxiuStatusEnum.Z.getCode()) || XtenantEnum.isSaasXtenant()
                                    || RankEnum.GJZB.hasAuthority(oaUserBO.getRank()))) {
                        updateWrapper.set(Shouhou::getBaoxiu, fm.getBaoxiu());
                        String comment = "·保修状态由【" + EnumUtil.getMessageByCode(BaoxiuStatusEnum.class, sh.getBaoxiu()) + "】更改为【" + EnumUtil.getMessageByCode(BaoxiuStatusEnum.class, fm.getBaoxiu()) + "】状态";
                        this.saveShouhouLog(id, comment, oaUserBO.getUserName(), null, false);
                    }else{
                        return R.error(StrUtil.format("保修状态修改条件: 处理状态为处理中,且有权限: 6c6 {}", XtenantEnum.isJiujiXtenant() ? ",非保改为在保,必须有: gjzb" : ""));
                    }
                }else{
                    return R.error("保修状态修改条件: 必须有权限: 6c6");
                }
            }

            if (CommenUtil.isNotNullZero(fm.getAreaid()) && CommenUtil.isNotNullZero(sh.getAreaid()) && !fm.getAreaid().equals(sh.getAreaid())) {
                //如果当前维修单存在库存，则不允许修改接件地
                List<Wxkcoutput> kcList = wxkcoutputService.list(new LambdaQueryWrapper<Wxkcoutput>().eq(Wxkcoutput::getWxid, sh.getId()));
                if (CollectionUtils.isNotEmpty(kcList)) {
                    return R.error("已有配件库存，不能修改接件地");
                }
                Optional.ofNullable(sh.getStats()).filter(CommenUtil::isNotNullZero)
                        .ifPresent(k -> {
                            throw new CustomizeException("只有处理中才能修改接件地信息");
                        });
                R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(sh.getAreaid());
                R<AreaInfo> areaInfoRNew = areaInfoClient.getAreaInfoById(fm.getAreaid());
                String comment = "接件地由" + areaInfoR.getData().getArea() + "店更改为" + areaInfoRNew.getData().getArea() + "店";
                this.saveShouhouLog(id, comment, oaUserBO.getUserName(), null, false);
            }

            Boolean isOutService = false;
            if (CommenUtil.isNotNullZero(fm.getServiceType()) && !fm.getServiceType().equals(sh.getServiceType())) {
                //增加判断是否存在出险的配件
                if(wxkcoutputService.lambdaQuery().eq(Wxkcoutput::getWxid, sh.getId())
                        .eq(Wxkcoutput::getServiceType, fm.getServiceType()).count()<=0){
                    throw new CustomizeException(StrUtil.format("不存在出险【{}】的配件", EnumUtil.getMessageByCode(BaoXiuTypeEnum.class, fm.getServiceType())));
                }
                updateWrapper.set(Shouhou::getServersOutUser, oaUserBO.getUserName()).set(Shouhou::getServersOutDtime, LocalDateTime.now());
                isOutService = true;
            }

            if (!isOutService && fm.getServiceType() != null && StringUtils.isEmpty(sh.getServersOutUser())) {
                updateWrapper.set(Shouhou::getServersOutUser, oaUserBO.getUserName()).set(Shouhou::getServersOutDtime, LocalDateTime.now());
                isOutService = true;
            }
            updateWrapper.set(Shouhou::getServiceType, fm.getServiceType());
            if (isOutService && StringUtils.isNotEmpty(EnumUtil.getMessageByCode(BaoXiuTypeEnum.class, fm.getServiceType()))) {
                shouhouMsgService.sendCollectMsg(id, "已为维修单出险【" + fm.getServiceType() + "】", oaUserBO.getUserName());
            }

            if (StringUtils.isNotEmpty(fm.getImei()) && !fm.getImei().equals(sh.getImei())) {
                if(XtenantEnum.isJiujiXtenant()){
                    throw new CustomizeException("串号已更新, 请刷新页面重试");
                }
                updateWrapper.set(Shouhou::getImei, fm.getImei());
                this.saveShouhouLog(id, "串号由【" + sh.getImei() + "】改为【" + fm.getImei() + "】", oaUserBO.getUserName(), null, false);
            }

            if (StringUtils.isNotEmpty(fm.getName()) && !fm.getName().equals(sh.getName())) {
                updateWrapper.set(Shouhou::getName, fm.getName());
                this.saveShouhouLog(id, "机型由【" + sh.getName() + "】改为【" + fm.getName() + "】", oaUserBO.getUserName(), null, false);
            }
            if (!Optional.ofNullable(fm.getProductColor()).orElse("").equals(Optional.ofNullable(sh.getProductColor()).orElse(""))) {
                if(StrUtil.isNotBlank(fm.getImei1())){
                    updateWrapper.set(Shouhou::getImei, fm.getImei1());
                    this.saveShouhouLog(id, "串号由【" + sh.getImei() + "】改为【" + fm.getImei1() + "】", oaUserBO.getUserName(), null, false);
                }
                updateWrapper.set(Shouhou::getProductColor, fm.getProductColor());
                this.saveShouhouLog(id, "规格由【" + sh.getProductColor() + "】改为【" + fm.getProductColor() + "】", oaUserBO.getUserName(), null, false);
            }
            if (!fm.getIsBakData().equals(sh.getIsBakData())) {
                shouhouExService.bakDataOp(fm.getIsBakData(), fm.getId(), "");
            }
        }

        //维修配件改价
        if (CollectionUtils.isNotEmpty(fm.getKcOutId())) {
            R<Boolean> editR = SpringUtil.getBean(ShouhouService.class).editPrice(sh, fm.getKcOutId(), fm.getKcPrice(), fm.getKcInprice(), fm.getKcPriceGs(), oaUserBO.getRank()
                    , oaUserBO.getUserName(), oaUserBO.getAreaKind1(), oaUserBO.getShowKind(), updateWrapper, shLogs, Boolean.TRUE.equals(fm.getIsJiujiServiceFlag()));
            if (editR.getCode() != ResultCode.SUCCESS) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return editR;
            }
        }

        updateWrapper.set(Shouhou::getComment, fm.getComment());
        if (fm.getWeixiuzuid() != null && fm.getWeixiuzuid() == 25 && !fm.getWeixiuzuid().equals(sh.getWeixiuzuid())) {
            return R.error("不可转入【预约处理组】！");
        }
        //只有售后维修的人才可以改到这几个组
        Boolean isGaiZu = true;
//        List<Integer> areaList1 = Arrays.asList(9, 26, 2, 67, 3, 27, 37, 17, 25);
//        List<Integer> areaList2 = Arrays.asList(50, 11, 4, 1, 10, 7);
//        List<Integer> areaList3 = Arrays.asList(50, 11, 8, 9, 4, 26, 2, 67, 103, 3, 27, 35, 37, 1, 17, 10, 25, 74);
//        Integer[] inKinds = new Integer[]{4, 16};
//        if (areaList1.contains(sh.getNowArea())) {
//            if (fm.getWeixiuzuid() != 1 && fm.getWeixiuzuid() != 4 && fm.getWeixiuzuid() != 25) {
//                isGaiZu = false;
//            }
//        } else if (areaList2.contains(sh.getNowArea())) {
//            if (fm.getWeixiuzuid() != 14 && fm.getWeixiuzuid() != 4 && fm.getWeixiuzuid() != 25) {
//                isGaiZu = false;
//            }
//        }
        if (sh.getWeixiuzuid() != null && sh.getWeixiuzuid() > 0) {
            Integer weixiuzuid1 = sh.getWeixiuzuid();
            if (fm.getWeixiuzuid() != null && !fm.getWeixiuzuid().equals(sh.getWeixiuzuid())) {
                updateWrapper.set(Shouhou::getPandian, false);
                if (isGaiZu) {
                    Weixiuzulogs logs = new Weixiuzulogs();
                    logs.setShouhouId(fm.getId());
                    logs.setDtime(LocalDateTime.now());
                    logs.setInuser(oaUserBO.getUserName());
                    logs.setWeixiuzuid1(weixiuzuid1);
                    logs.setWeixiuzuid2(fm.getWeixiuzuid());
                    weixiuzulogsService.save(logs);
                    ShouhouMsgPushMessageBo message = getWeiXiuZuMsgPushMessage(fm, oaUserBO, sh, weixiuzuid1);
                    pusheMessageList.add(message);
                }
            }
        } else {
            updateWrapper.set(Shouhou::getWeixiuStartdtime, LocalDateTime.now());
            Weixiuzulogs logs = new Weixiuzulogs();
            logs.setShouhouId(fm.getId());
            logs.setDtime(LocalDateTime.now());
            logs.setInuser(oaUserBO.getUserName());
            logs.setWeixiuzuid1(null);
            logs.setWeixiuzuid2(fm.getWeixiuzuid());
            weixiuzulogsService.save(logs);
            ShouhouMsgPushMessageBo message = getWeiXiuZuMsgPushMessage(fm, oaUserBO, sh, null);
        }

        Integer stats = sh.getStats() == null ? 0 : sh.getStats();
        if (fm.getWeixiuzuid() == null) {
            updateWrapper.set(Shouhou::getWeixiuzuid, null).set(Shouhou::getWeixiuzuidJl, null);
        } else {
            if (sh.getWeixiuzuid() == null) {
                updateWrapper.set(Shouhou::getWeixiuzuidJl, fm.getWeixiuzuid());
            } else if (fm.getWeixiuzuid() != null && (fm.getWeixiuzuid() != 5 && stats != 1 || fm.getWeixiuzuid() != 4)) {
                updateWrapper.set(Shouhou::getWeixiuzuidJl, fm.getWeixiuzuid());
            }
            if (isGaiZu) {
                updateWrapper.set(Shouhou::getWeixiuzuid, fm.getWeixiuzuid());
            }
        }
        //查询是否有收货地址则记录
        List<Addinfops> addinfopsList = addinfopsService.list(new QueryWrapper<Addinfops>().lambda().eq(Addinfops::getType, 2)
                .eq(Addinfops::getBindId, fm.getId()).orderByAsc(Addinfops::getType));
        Addinfops addr = null;
        if (CollectionUtils.isNotEmpty(addinfopsList)) {
            addr = addinfopsList.get(0);
        }
        String temAddress = "";
        String tmpRecover = "";
        ShouhouAddressInfo addressInfo = fm.getAddressInfo();
        if (fm.getIsWeb() != null && fm.getIsWeb()) {
            Integer webStats = sh.getWebstats() == null ? 0 : sh.getWebstats();
            if (webStats <= 2) {
                updateWrapper.set(Shouhou::getWebtype1, fm.getWebtype1()).set(Shouhou::getWebtype2, fm.getWebtype2());
            }
            if (webStats < 4 && addressInfo != null) {
                addinfopsService.remove(new QueryWrapper<Addinfops>().lambda().eq(Addinfops::getBindId, fm.getId()).in(Addinfops::getType, Arrays.asList(1, 2)));
                if (fm.getWebstats() != null && (fm.getWebstats() == 2) || fm.getWebstats() == 4) {
                    Addinfops addinfops = new Addinfops();
                    addinfops.setReciver(addressInfo.getReciver1());
                    addinfops.setConsignee(addressInfo.getConsignee1());
                    addinfops.setAddress(addressInfo.getAddress1());
                    addinfops.setCityid(addressInfo.getCityid1());
                    addinfops.setType(1);
                    addinfops.setBindId(fm.getId());
                    addinfopsService.save(addinfops);
                    if (addressInfo.getIsAddress2() != null && addressInfo.getIsAddress2()) {
                        addinfops.setType(2);
                        addinfopsService.save(addinfops);
                        temAddress = addressInfo.getAddress1();
                        tmpRecover = addressInfo.getReciver1();

                    } else if (StringUtils.isNotEmpty(addressInfo.getReciver2()) || StringUtils.isNotEmpty(addressInfo.getAddress2())) {

                        addinfops.setReciver(addressInfo.getReciver2());
                        addinfops.setConsignee(addressInfo.getConsignee2());
                        addinfops.setAddress(addressInfo.getAddress2());
                        addinfops.setCityid(addressInfo.getCityid2());
                        addinfops.setType(2);
                        addinfops.setBindId(fm.getId());
                        addinfops.setMobile(addressInfo.getPhone2());
                        addinfops.setPosition(addressInfo.getPosition2());
                        addinfops.setDetailAddress(addressInfo.getDetailAddress2());
                        addinfopsService.save(addinfops);
                        temAddress = addressInfo.getAddress2();
                        tmpRecover = addressInfo.getReciver2();
                    }

                } else {
                    if (StringUtils.isNotEmpty(addressInfo.getReciver2()) || StringUtils.isNotEmpty(addressInfo.getAddress2())) {
                        Addinfops addinfops = new Addinfops();
                        addinfops.setReciver(addressInfo.getReciver2());
                        addinfops.setConsignee(addressInfo.getConsignee2());
                        addinfops.setAddress(addressInfo.getAddress2());
                        addinfops.setCityid(addressInfo.getCityid2());
                        addinfops.setBindId(fm.getId());
                        addinfops.setType(2);
                        addinfops.setMobile(addressInfo.getPhone2());
                        addinfops.setPosition(addressInfo.getPosition2());
                        addinfops.setDetailAddress(addressInfo.getDetailAddress2());
                        addinfopsService.save(addinfops);
                        temAddress = addressInfo.getAddress2();
                        tmpRecover = addressInfo.getReciver2();
                    }

                }
            }
        } else {
            if (addressInfo != null && (StringUtils.isNotEmpty(addressInfo.getReciver2()) || StringUtils.isNotEmpty(addressInfo.getAddress2()))) {
                addinfopsService.remove(new QueryWrapper<Addinfops>().lambda().eq(Addinfops::getType, 2).eq(Addinfops::getBindId, fm.getId()));

                Addinfops addinfops = new Addinfops();
                addinfops.setReciver(addressInfo.getReciver2());
                addinfops.setConsignee(addressInfo.getConsignee2());
                addinfops.setAddress(addressInfo.getAddress2());
                addinfops.setCityid(addressInfo.getCityid2());
                addinfops.setType(2);
                addinfops.setBindId(fm.getId());
                addinfops.setMobile(addressInfo.getPhone2());
                addinfops.setPosition(addressInfo.getPosition2());
                addinfops.setDetailAddress(addressInfo.getDetailAddress2());
                addinfopsService.save(addinfops);
                temAddress = addressInfo.getAddress2() == null ? "" : addressInfo.getAddress2();
                tmpRecover = addressInfo.getReciver2();
                //同步地址信息到物流单
                //获取物流单号
                Optional.ofNullable(sh)
                        .flatMap(shh -> SpringUtil.getBean(WuliuService.class).lambdaQuery().eq(Wuliu::getDanhaobind, shh.getId())
                                .eq(Wuliu::getWutype, WuLiuType.REPAIR_ORDER_DISPATCH.getCode()).gt(Wuliu::getDtime, shh.getModidate())
                                .orderByDesc(Wuliu::getId).list().stream().findFirst())
                        .ifPresent(wuliu -> {
                            Areainfo areainfo = Optional.ofNullable(areainfoService.getById(sh.getNowArea())).orElse(new Areainfo());
                            Result<String> upWuLiuR = SpringUtil.getBean(WuliuStockCloud.class).updateWuliuV2(LambdaBuild.create(new WuLiuUpdateReqV2())
                                    .set(WuLiuUpdateReqV2::setRname, addressInfo.getReciver2()).set(WuLiuUpdateReqV2::setRaddress, addressInfo.getAddress2() + addressInfo.getDetailAddress2())
                                    .set(WuLiuUpdateReqV2::setRcityId, addressInfo.getCityid2())
                                    .set(WuLiuUpdateReqV2::setWuliuId, wuliu.getId())
                                    .set(WuLiuUpdateReqV2::setSareaId, areainfo.getId())
                                    .set(WuLiuUpdateReqV2::setSname, areainfo.getPrintName())
                                    .set(WuLiuUpdateReqV2::setSmobile, areainfo.getCompanyTel1())
                                    .set(WuLiuUpdateReqV2::setSaddress, areainfo.getCompanyAddress())
                                    .build());
                            shLogs.add(new ShouhouLogNewBo(id,StrUtil.format("同步到物流单信息结果:{}",upWuLiuR.getUserMsg()), oaUserBO.getUserName(), null, false));
                            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"同步到物流单信息结果:{}",upWuLiuR.getUserMsg());
                        });
            }
        }
        //如果修改收货地址则记录
        if (addr != null) {
            if (StringUtils.isEmpty(tmpRecover) && StringUtils.isEmpty(temAddress)) {
                if (fm.getIsWeb() != null && fm.getIsWeb() && (sh.getWebstats() == null || sh.getWebstats() < 4)) {
                    String comment = String.format("【修改收货地址】修改为无：修改前收货人是 %s，收货地址是 %s", addr.getReciver(), addr.getAddress());
                    shLogs.add(new ShouhouLogNewBo(id, comment, oaUserBO.getUserName(), null, false));
                }
            } else {
                if (!tmpRecover.equals(addr.getReciver()) || !temAddress.equals(addr.getAddress() == null ? "" : addr.getAddress())) {
                    String comment = String.format("【修改收货地址】修改前收货人是 %s，收货地址是 %s；修改后收货人是 %s，收货地址是 %s；", addr.getReciver(), addr.getAddress(), tmpRecover, temAddress);
                    shLogs.add(new ShouhouLogNewBo(id, comment, oaUserBO.getUserName(), null, false));
                }
            }
        } else if (addr == null && StringUtils.isNotEmpty(temAddress)) {
            String comment = String.format("【添加收货地址】 %s，收货人是 %s", temAddress, tmpRecover);
            shLogs.add(new ShouhouLogNewBo(id, comment, oaUserBO.getUserName(), null, false));
        }
        if ((sh.getStats() == null || sh.getStats() == 0) && fm.getAreaid() != 0) {
            updateWrapper.set(Shouhou::getAreaid, fm.getAreaid());
        }
        if (fm.getSxsex() != null) {
            updateWrapper.set(Shouhou::getSxsex, fm.getSxsex());
        }
        if (StringUtils.isNotEmpty(fm.getDeviceid())) {
            updateWrapper.set(Shouhou::getDeviceid, fm.getDeviceid());
        }
        if (StringUtils.isNotEmpty(fm.getDevicepwd())) {
            updateWrapper.set(Shouhou::getDevicepwd, fm.getDevicepwd());
        }
//        List<Integer> zhihuanAreas = Arrays.asList(22, 16, 1, 2, 3, 27, 37, 67, 103, 50, 8, 25, 74, 9, 17, 4, 10, 11, 26);
        if (fm.getWxkind() != null && fm.getWxkind() == 5 && 5 != sh.getWxkind()) {
            updateWrapper.set(Shouhou::getWxkind, 5);
        } else if (fm.getWxkind() != null && fm.getWxkind() > 0 && !sh.getIsQuJiE().equals(1) && !fm.getWxkind().equals(sh.getWxkind())) {
            updateWrapper.set(Shouhou::getWxkind, fm.getWxkind());
        }
        //修改跟进人
        if (StringUtils.isNotEmpty(fm.getGjUser()) && !fm.getGjUser().equals(sh.getGjUser())) {
            updateWrapper.set(Shouhou::getGjUser, fm.getGjUser());
        }
        if (StringUtils.isNotEmpty(fm.getWuliyou())) {
            updateWrapper.set(Shouhou::getWuliyou, fm.getWuliyou());
        }

        updateWrapper.eq(Shouhou::getId, id);
        Boolean flag = super.update(updateWrapper);
        if (flag) {
            //修不好原因修改
            cantFixReasonService.saveData(new SaveCantFixReasonVO(fm.getId(),fm.getReasonList()));
            if (fm.getStats() != null && (WxStatusEnum.YXH.getCode().equals(fm.getStats()) || WxStatusEnum.XBH.getCode().equals(fm.getStats()))) {
                //处理锁定维修配件
                R<Boolean> operateKcR = shouHouPjService.wxpjNolockOutkc(id, "系统");
                if (operateKcR == null || operateKcR.getCode() != ResultCode.SUCCESS) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return R.error("出库失败");
                }

                LocalDateTime modidate = sh.getModidate();
                LocalDateTime addqudaotime = null;
                List<ShouhouQudao> qudaoList = shouhouQudaoService.list(new QueryWrapper<ShouhouQudao>().lambda().eq(ShouhouQudao::getShouhouid, id).select(ShouhouQudao::getDtime));
                if (CollectionUtils.isNotEmpty(qudaoList)) {
                    addqudaotime = qudaoList.get(0).getDtime();
                }
                ShouhouTimer timer = new ShouhouTimer();
                timer.setShouhouid(id);
                if (addqudaotime != null) {
                    Duration duration = Duration.between(addqudaotime, LocalDateTime.now());
                    timer.setXiuhao((int) duration.toMinutes());
                    shouhouTimerService.addShouhouTimer(timer);
                } else {
                    Duration duration = Duration.between(modidate, LocalDateTime.now());
                    timer.setXiuhao((int) duration.toMinutes());
                    shouhouTimerService.addShouhouTimer(timer);
                }
                if (fm.getStats() != null && fm.getStats() == 1) {
                    LocalDateTime toareaTime = baseMapper.getShouhouToAreaTime(id);
                    if (toareaTime != null) {
                        Duration duration = Duration.between(modidate, LocalDateTime.now());
                        timer.setXiuhao((int) duration.toMinutes());
                        shouhouTimerService.addShouhouTimer(timer);
                    }
                }
            }
            if(CollUtil.isNotEmpty(fm.getFiles())){
                attachmentsService.attachmentsAdd(fm.getFiles(), id, AttachmentsEnum.SHOUHOU.getCode(), 0, oaUserBO.getUserId(), fm.getModidate());
            }
            //取消服务的时候,清理服务的关联关系
            if(CommenUtil.isNotNullZero(sh.getServiceType()) && CommenUtil.isNullOrZero(fm.getServiceType())){
                wxkcoutputService.cancelServiceType(sh.getId(),sh.getServiceType());
                shLogs.add(new ShouhouLogNewBo(id, StrUtil.format("取消服务【{}】",Optional.ofNullable(EnumUtil.getEnumByCode(BaoXiuTypeEnum.class,sh.getServiceType()))
                        .map(BaoXiuTypeEnum::getMessage).orElseGet(()->String.valueOf(fm.getServiceType()))), oaUserBO.getUserName(), null, false));
            }

            if (CollUtil.isNotEmpty(fm.getFiles2())) {
                attachmentsService.attachmentsAdd(fm.getFiles2(), id, AttachmentsEnum.SHOUHOU.getCode(), 1, oaUserBO.getUserId(),fm.getModidate());
            }
            Integer yuyueId = sh.getYuyueid() == null ? 0 : sh.getYuyueid();
            Boolean isZy = sh.getIszy() == null ? false : sh.getIszy();

            if (!isZy) {
                //已修好或修不好 更改预约单为完成
                if (yuyueId != 0 && fm.getStats() != null && (fm.getStats() == 1 || fm.getStats() == 3)) {
                    yuyuecomplete(yuyueId);
                }
                //如果是快修预约，则通知工程师
                if (sh.getWxkind() != null && sh.getWxkind() == 6 && StringUtils.isNotEmpty(fm.getWeixiuren()) && !fm.getWeixiuren().equals(sh.getWeixiuren())) {
                    R<Ch999UserVo> ch999UserVoR = userInfoClient.getCh999UserByUserName(fm.getWeixiuren());
                    if (ch999UserVoR.getCode() == ResultCode.SUCCESS && ch999UserVoR.getData() != null) {
                        Integer userId = ch999UserVoR.getData().getCh999Id();
                        String userName = fm.getUsername();
                        String mobile = ch999UserVoR.getData().getMobile();
                        if (userId != null) {
                            String url = moaUrlSource.getWxDetail(id.toString());
                            String content = "您有一个售后快修需要处理，维修单号：<a href='" + url + "'>" + sh.getId().toString() + "</a>  \n" + DateUtil.localDateTimeToString(LocalDateTime.now());
                            String wxMsg = "您有一个售后快修需要处理，维修单号：" + sh.getId().toString();

                            weixinUserService.senWeixinAndOaMsg(wxMsg, content, url, userId.toString(), OaMesTypeEnum.SHTZ.getCode().toString());
                        }
                    }
                }

            } else {
                //如果是中邮，对应预约单的全部维修单处理完成，则自动设置预约单为 已完成
                if (yuyueId != 0 && fm.getStats() != null && (fm.getStats() == 1 || fm.getStats() == 3)) {
                    //判断是否所有维修单都已修好或修不好，最后才决定是否要完结预约单。
                    List<Shouhou> shouhouList = baseMapper.selectList(new LambdaQueryWrapper<Shouhou>().notIn(Shouhou::getStats, Arrays.asList(1, 3))
                            .eq(Shouhou::getYuyueid, yuyueId).ne(Shouhou::getId, sh.getId()));
                    if (CollectionUtils.isEmpty(shouhouList)) {
                        yuyuecomplete(yuyueId);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(pusheMessageList)) {
                pusheMessageList.stream().forEach(message -> {
                    this.pushMessage(message, true);
                });
            }
        }
        if (!flag) {
            return R.error("操作失败");
        }

        //维修状态是处理中，并且填写了外送渠道
        ShouhouQudao qudaoEntity = shouhouQudaoService.loadOrCreateShouhouQudao(sh, fm, shLogs);
        if (qudaoEntity != null) {
            shouhouQudaoService.updateFanxiuShouhouQudao(fm.getImei(), qudaoEntity.getId());
        }

        if (CollectionUtils.isNotEmpty(shLogs)) {
            shLogs.stream().forEach(logs -> {
                this.saveShouhouLog(logs.getShouhouId(), logs.getComment(), oaUserBO.getUserName(), null, logs.getIsWeb());
            });
        }
        //存在优惠费用
        boolean existYouhuiFeiyong = (sh.getYouhuifeiyong() == null || sh.getYouhuifeiyong().compareTo(BigDecimal.ZERO) <= 0)
                && sh.getFeiyong() != null && sh.getFeiyong().compareTo(BigDecimal.ZERO) > 0;
        //已付款小于费用
        boolean isYifumLtFeiyong = (sh.getYifum() == null || sh.getYifum().compareTo(sh.getFeiyong()) < 0);
        // 改为已修好，自动使用优惠码
        boolean isUserNumberCard = Objects.equals(fm.getStats(), 1)
                && existYouhuiFeiyong && oaUserBO.getXTenant() < 1000
                && CommenUtil.isNullOrZero(sh.getServiceType())
                && isYifumLtFeiyong
                // 排除增值机,不自动使用优惠码
                && ObjectUtil.defaultIfNull(sh.getIshuishou(), 0) != 2;
        if(Integer.valueOf(0).equals(sh.getIsQuJiE())){
            //未取机,更新成功后重新计算费用
            shouHouPjService.updateShouhouFeiyong(sh.getId());
        }
        if (isUserNumberCard) {

            Boolean isSoft = sh.getIssoft() == null ? Boolean.FALSE : sh.getIssoft();

            BigDecimal modifyPrice = baseMapper.getModifyPriceByShouHouId(fm.getId());
            if (modifyPrice != null && modifyPrice.compareTo(BigDecimal.ZERO) > 0) {
                return R.success("操作成功");
            }
            R<List<NumberCardRes>> kxYouhuimasR = numberCardService.getKxYouhuimas(sh.getUserid(), isSoft, sh.getId());
            if (kxYouhuimasR.getCode() == ResultCode.SUCCESS && CollectionUtils.isNotEmpty(kxYouhuimasR.getData())) {

                List<NumberCardRes> youhuiMas = kxYouhuimasR.getData();
                youhuiMas = youhuiMas.stream().sorted(Comparator.comparing(NumberCardRes::getTotal).reversed()).collect(Collectors.toList());
                youhuiMas.stream().forEach(item -> {
                    if (sh.getFeiyong().compareTo(item.getLimitprice()) > 0 || isSoft) {
                        R<Boolean> booleanR = shouhouExService.useYouhuiMa(sh.getId(), item.getCardID(), "系统");
                        if (booleanR.getCode() == ResultCode.SUCCESS) {
                            return;
                        }
                    }
                });
            }
        }
        //当处理状态修改为【已修好】，点击【保存修改】后
        //已修好  回购机 接件人是系统 用户是76783
        if (XtenantEnum.isJiujiXtenant() && Objects.equals(fm.getStats(), DealStatsEnum.YXH.getCode()) && Objects.equals(fm.getIshuishou(), 1)
                && Objects.equals(fm.getInuser(),"系统") && Objects.equals(fm.getUserid(),76783L)) {
            //判断接件地是贵州还是云南
            List<AreaInfo> areaInfoList = Optional.ofNullable(areaInfoClient.listAll()).filter(R::isSuccess)
                    .map(R::getData).orElseThrow(() -> new CustomizeException("调用接口错误"));
            AreaInfo areaInfo = areaInfoList.stream().filter(ar-> Objects.equals(sh.getAreaid(), ar.getId())).findFirst().orElse(new AreaInfo());
            //默认设置为云南的售后
            String area = "h1";
            Integer areaId = areaInfoList.stream().filter(ar -> Objects.equals(ar.getArea(), "h1")).findFirst().orElse(new AreaInfo()).getId();
            //跟据pid  52是贵州  53是云南
            if (Objects.equals(areaInfo.getPid(),52)){
                area = "h2";
                areaId = areaInfoList.stream().filter(ar -> Objects.equals(ar.getArea(), "h2")).findFirst().orElse(new AreaInfo()).getId();
            }
            if(CommenUtil.isNullOrZero(areaId) || CommenUtil.isNullOrZero(sh.getMkcId())){
                return R.success("操作成功");
            }
            //系统自动将退款良品调拨到h1/h2，并出现toast提示【该商品系统已生成调拨h1/h2指令，请及时发出】
            String oaUrl = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST)).filter(R::isSuccess)
                    .map(R::getData).filter(StrUtil::isNotBlank).orElseThrow(() -> new CustomizeException("调用接口错误"));
            String url = StrUtil.format("{}/recover.svc/rest/RecyclePorductTransfer", oaUrl);
            Map<String, String> headers = new HashMap<>(1);
            headers.put("Content-Type", "application/json");
            Map<String, String> params = new HashMap<>(4);
            params.put("mkcId", String.valueOf(sh.getMkcId()));
            params.put("toAreaId", String.valueOf(areaId));
            params.put("opUser", oaUserBO.getUserName());
            params.put("logComment", "自动生成调拨操作");
            String response = HttpClientUtil.post(url, JSONObject.toJSONString(params), headers);
            R r;
            try {
                log.debug("自动调拨操作响应结果:{}",response);
                r = JSON.parseObject(response, R.class);
            } catch (Exception e) {
                AtomicReference<String> msgRef = new AtomicReference<>("");
                RRExceptionHandler.logError("自动调拨操作",Dict.create().set("params",params).set("url",url).set("response",response),e,msgRef::set);
                return R.success(msgRef.get());
            }
            if (r.getCode() == 0 || Objects.equals(r.getData(), Boolean.TRUE)) {
                String format = StrUtil.format("该商品系统已生成调拨{}指令，请及时发出!", area);
                return R.success(format);
            } else {
                R<Boolean> success = R.success(StrUtil.format("自动调拨异常:{}", r.getUserMsg()));
                return success.addBusinessLog("调用接口/recover.svc/rest/RecyclePorductTransfer异常");
            }
        }
        return R.success("操作成功");
    }

    private ShouhouMsgPushMessageBo getWeiXiuZuMsgPushMessage(ShouhouInfoReq fm, OaUserBO oaUserBO, ShouhouDtBo sh, Integer weixiuzuid1) {
        WeixiuzuKind wxGroupKind = null;
        if(CommenUtil.isNotNullZero(weixiuzuid1)){
            wxGroupKind = weixiuzuKindService.getById(weixiuzuid1);
        }
        WeixiuzuKind wxKind = null;
        if (CommenUtil.isNotNullZero(fm.getWeixiuzuid())) {
            wxKind = weixiuzuKindService.getById(fm.getWeixiuzuid());
        }
        Map<String, String> tmpData = new HashMap<>();
        tmpData.put("groupname1", weixiuzuid1 == null ? "" : wxGroupKind != null ? wxGroupKind.getName() : "");
        tmpData.put("groupname2", fm.getWeixiuzuid() == null ? "" : wxKind != null ? wxKind.getName() : "");
        ShouhouMsgPushMessageBo message = new ShouhouMsgPushMessageBo();
        //改组内容配置id 19
        message.setMsgId(19);
        message.setShouhouId(fm.getId());
        message.setAreaId(sh.getAreaid());
        message.setUserId(sh.getUserid() == null ? null : sh.getUserid().intValue());
        message.setTmpData(tmpData);
        message.setOptUser(oaUserBO.getUserName());
        return message;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> remarkAndUpdatePrice(RemarkAndUpdatePriceReq req) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }
        if(!Optional.ofNullable(oaUserBO.getRank()).orElse(new ArrayList<>()).contains("fyxg")){
            return R.error("没有fyxg权限无法操作");
        }
        // 判断是否为 回收换新补贴加价支付配件
        Integer wxPjId = req.getWxPjId();
        Wxkcoutput wxkcoutput = Optional.ofNullable(wxkcoutputService.getById(wxPjId)).orElseThrow(() -> new CustomizeException("维修配件查询为空"));
        if(!Wxkcoutput.PartTypeEnum.HUI_SHOU_ZHE_JIA_PAY.getCode().equals(wxkcoutput.getPartType())){
            return R.error("非回收换新补贴加价支付配件无法修改");
        }
        //获取到原始的卖价
        BigDecimal price = Optional.ofNullable(wxkcoutput.getPrice()).orElse(BigDecimal.ZERO);
        BigDecimal updatePrice = req.getUpdatePrice();
        boolean updatedKc = wxkcoutputService.lambdaUpdate().eq(Wxkcoutput::getId, wxPjId)
                .set(Wxkcoutput::getPrice, updatePrice)
                .set(Wxkcoutput::getName,"回收补贴费用退回")
                .update();
        if(!updatedKc){
             throw new CustomizeException("维修配件修改价格失败");
        }
        Integer shouHouId = req.getShouHouId();
        Shouhou shouHou = Optional.ofNullable(this.getById(shouHouId)).orElseThrow(() -> new CustomizeException("维修单查询为空"));
        BigDecimal feiYong = Optional.ofNullable(shouHou.getFeiyong()).orElse(BigDecimal.ZERO);
        BigDecimal newFeiYong = feiYong.subtract(price.subtract(updatePrice));
        boolean updatedSh = this.lambdaUpdate().eq(Shouhou::getId, shouHouId)
                .set(Shouhou::getFeiyong, newFeiYong)
                .update();
        if(!updatedSh){
            throw new CustomizeException("维修单费用修改失败");
        }
        String logComment = StrUtil.format("修改回收补贴费用退回价格由：{}修改为：{}",  price.setScale(2, RoundingMode.HALF_UP),  updatePrice.setScale(2, RoundingMode.HALF_UP));
        //日志记录
        saveShouhouLog(shouHouId,logComment+"，原因："+req.getRemark(), oaUserBO.getUserName(), null, Boolean.FALSE);
        return R.success("修改成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> editPrice(WxPjEditPriceReq req) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }
        List<ShouhouLogNewBo> shLogs = new LinkedList<>();
        ShouhouDtBo sh = baseMapper.getShouhouDtById(req.getId());
        LambdaUpdateWrapper<Shouhou> updateWrapper = new LambdaUpdateWrapper<>();
        try {
            ShouhouService shouhouService = SpringUtil.getBean(ShouhouService.class);
            R<Boolean> editPriceR = shouhouService.editPrice(sh, req.getKcOutId(), req.getKcPrice(), req.getKcInprice(), req.getKcPriceGs()
                    , oaUserBO.getRank(), oaUserBO.getUserName(), oaUserBO.getAreaKind1(), oaUserBO.getShowKind(), updateWrapper, shLogs, false);
            if(!editPriceR.isSuccess()){
                return editPriceR;
            }
            if (!updateWrapper.isEmptyOfNormal()) {
                updateWrapper.eq(Shouhou::getId, req.getId());
            }

            Boolean flag = shouhouService.update(updateWrapper);
            if (flag) {
                shLogs.stream().forEach(e -> {
                    this.saveShouhouLog(e.getShouhouId(), e.getComment(), e.getInuser(), e.getType(), e.getIsWeb());
                });
            } else {
                return R.error("修改失败");
            }

        } catch (Exception e) {

            log.error("修改异常", e);
//            e.printStackTrace();
            return R.error("修改失败");
        }
        return R.success("修改成功");
    }

    @Override
    public Integer updateYouhuiMaUseInfo(Integer shouhouId, String youhuiMa, BigDecimal youhuimaTotal) {
        return baseMapper.updateYouhuiMaUseInfo(shouhouId, youhuiMa, ObjectUtil.defaultIfNull(youhuimaTotal,BigDecimal.ZERO)
                .setScale(NumberConstant.FOUR, RoundingMode.HALF_UP));
    }

    @Override
    public ShouhouHuanBo getShouhouHuan(Integer shouhouId) {
        return baseMapper.getShouhouHuan(shouhouId);
    }

    @Override
    public Integer getBuyAreaIdByShouhouId(Integer shouhouId) {

        //处理新机单
        Integer areaId = null;
        Shouhou shouhou = super.getById(shouhouId);
        if (CommenUtil.isNotNullZero(shouhou.getBuyareaid())) {
            return shouhou.getBuyareaid();
        }
        if (CommenUtil.isNotNullZero(shouhou.getFromshouhouid())) {
            Shouhou sh = super.getById(shouhou.getFromshouhouid());
            if (CommenUtil.isNotNullZero(sh.getSubId())) {
                shouhou = sh;
            }
        }

        if (CommenUtil.isNotNullZero(shouhou.getSubId())) {
            Integer isHuishou = shouhou.getIshuishou();

            if (CommenUtil.isNotNullZero(isHuishou) && isHuishou.equals(1)) {
                areaId = shouhouExMapper.getBuyAreaIdByRecoverMarketInfo(shouhou.getSubId());
            } else {
                areaId = shouhouExMapper.getBuyAreaIdBySubId(shouhou.getSubId());
            }
        }
//        else {
//            areaId = shouhou.getAreaid();
//        }

        return areaId;
    }

    @Override
    public List<Long> getShouhouRepaireUserInfo(List<Long> userIds) {
        return baseMapper.getShouhouRepaireUserIds(userIds);
    }


    @Override
    public ShouhouRepairInfoRes getRepairRecordByImei(String imei) {

        ShouhouRepairInfoRes res = new ShouhouRepairInfoRes();
        if (StringUtils.isEmpty(imei)) {
            return res;
        }

        String jsonStr = HttpClientUtil.get(sysConfigService.getServiceInfoUrl((int) Namespaces.get(), imei));
        if (StringUtils.isNotEmpty(jsonStr)) {
            log.info(jsonStr);
            String stats = JSONObject.parseObject(jsonStr).get("stats").toString();
            if (stats.equals("1")) {
                if (JSONObject.parseObject(jsonStr).get("tradedate") != null) {
                    res.setBaoxiuTradedate(JSONObject.parseObject(jsonStr).get("tradedate").toString());
                }
                if (JSONObject.parseObject(jsonStr) != null && JSONObject.parseObject(jsonStr).get("BaoxiuEndDate") != null) {
                    res.setBaoxiuEndDate(JSONObject.parseObject(jsonStr).get("BaoxiuEndDate").toString());
                }
            }
        }

        //查询维修历史记录
        List<ShouhouBasicInfo> shouhouList = baseMapper.getRepairHistoryByImei(imei);
        if (CollectionUtils.isEmpty(shouhouList)) {
            return res;
        }

        List<WxPjBasicInfo> wxPjList = baseMapper.getRepairPjListByImei(imei);


        shouhouList = shouhouList.stream().map(e -> {
            e.setBaoXiuText(EnumUtil.getMessageByCode(BaoxiuStatusEnum.class, e.getBaoXiu()));
            e.setStatusText(EnumUtil.getMessageByCode(WxStatusEnum.class, e.getStatus()));

            if (CollectionUtils.isNotEmpty(wxPjList)) {
                List<WxPjBasicInfo> pjItem = wxPjList.stream().filter(w -> w.getShouhouId().equals(e.getShouhouId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(pjItem)) {
                    e.setPjInfoList(pjItem);

                    List<Integer> cids = pjItem.stream().map(c -> c.getCid()).collect(Collectors.toList());
                    if (CommenUtil.compareList(Arrays.asList(26, 53, 68, 410, 476), cids) && CommenUtil.compareList(Arrays.asList(24, 25, 522, 31), cids)) {
                        e.setLinkItem("屏幕维修+主板维修");
                    } else if (CommenUtil.compareList(Arrays.asList(26, 53, 68, 410, 476), cids)) {
                        e.setLinkItem("主板维修/严重拆修");
                    } else if (CommenUtil.compareList(Arrays.asList(24, 25, 522, 31), cids)) {
                        e.setLinkItem("屏幕维修");
                    } else if (CommenUtil.compareList(Arrays.asList(27, 28, 29, 30, 54, 69, 159, 482, 393, 40), cids)) {
                        e.setLinkItem("小拆修");
                    } else {
                        e.setLinkItem("");
                    }
                }
            }

            return e;
        }).collect(Collectors.toList());

        res.setRepairHistory(shouhouList);
        return res;
    }


    @Override
    public ShouhouTuihuanRes huan(Integer id) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        ShouhouTuihuanRes shouhouTuihuanRes = new ShouhouTuihuanRes();
        //退款信息
        ShouhouTuihuan huan =
                shouhouTuihuanService.listSqlServer(new LambdaQueryWrapper<ShouhouTuihuan>().eq(ShouhouTuihuan::getShouhouId,
                        id).in(ShouhouTuihuan::getTuihuanKind, Arrays.asList(1, 2, 3, 4)).eq(ShouhouTuihuan::getIsdel
                        , 0)).stream().findFirst().orElse(null);
        //订单信息
        Shouhou info = this.list(new LambdaQueryWrapper<Shouhou>().eq(Shouhou::getId, id).ne(Shouhou::getUserid,
                76783)).stream().findFirst().orElse(null);
        ShouhouTuihuanRes.ShouhouRes shouhou = new ShouhouTuihuanRes.ShouhouRes();
        BeanUtils.copyProperties(info, shouhou);
        shouhou.setCk_area(info.getToareaid() == null ? info.getAreaid() : info.getToareaid());
        if (info.getTradedate() != null && info.getModidate() != null) {
            shouhou.setD1((int) Duration.between(info.getTradedate(), info.getModidate()).toDays());
        }
        //从订单取
        BigDecimal sub_buypriceM = BigDecimal.valueOf(-1);
        BigDecimal coinM = BigDecimal.ZERO;
        //赠品金额
        BigDecimal giftPrice = BigDecimal.ZERO;
        //红包金额
        BigDecimal wxhongbao = BigDecimal.ZERO;
        //九机白条金额
        BigDecimal baitiaoPrice = BigDecimal.ZERO;
        //库分期金额
        BigDecimal kuBaiTiaoPrice = BigDecimal.ZERO;
        //非库分期金额
        BigDecimal nkuBaiTiaoPrice = BigDecimal.ZERO;
        int baksetId = 0;
        //ovg 返现金额
        BigDecimal ovgSaveMoney = BigDecimal.ZERO;
        //当前用户余额
        BigDecimal userSaveMoney = BigDecimal.ZERO;
        //交易天数
        int tradeDay = 0;
        //交易类别 1、新机 2、优品 3、良品
        int tradeType = 0;
        //交易日期
        LocalDateTime tradeDate = null;
        //商品附件
        String peizhi = "";
        //发票类别 1 销售 2 良品
        String piaoType = "";
        //用户1年内折价退换数量
        Integer afterServicesDiscount = 0;
        //和包分期金额
        BigDecimal hebaoPrice = BigDecimal.ZERO;

        Integer mkcId = 0;

        Boolean boolUserFlag = Boolean.FALSE;
        Integer buyUserId = 0;

        //退换配置
        List<TuihuanConfigRes> tuihunConfig = new ArrayList<>();
        if (info != null && huan == null) {
            mkcId = info.getMkcId() == null ? 0 : info.getMkcId();
            Integer buySubId = info.getSubId();
            Integer ishuishou = info.getIshuishou() == null ? 0 : info.getIshuishou();
            int userid = 0;
            BigDecimal yifuM = BigDecimal.ZERO;

            TuihuanBuyPriceOutBo tuihuanBuyPrice = new TuihuanBuyPriceOutBo();
            tuihuanBuyPrice.setTradeDay(0);
            tuihuanBuyPrice.setTradeType(tradeType);
            tuihuanBuyPrice = shouhouTuihuanService.getTuihuanBuyPrice(tuihuanBuyPrice);

            sub_buypriceM = tuihuanBuyPrice.getPrice();
            coinM = tuihuanBuyPrice.getCoinM();
            giftPrice = tuihuanBuyPrice.getGiftPrice();
            baitiaoPrice = tuihuanBuyPrice.getBaitiaoPrice();
            kuBaiTiaoPrice = tuihuanBuyPrice.getKuBaitiaoPrice() == null ? BigDecimal.ZERO : tuihuanBuyPrice.getKuBaitiaoPrice();
            if (tuihuanBuyPrice.getBasketId() != null) {
                baksetId = tuihuanBuyPrice.getBasketId();
            }

            yifuM = tuihuanBuyPrice.getYifuM();
            userid = tuihuanBuyPrice.getUserId();
            tradeDay = Math.abs(tuihuanBuyPrice.getTradeDay());
            tradeDate = tuihuanBuyPrice.getTradeDate();
            tradeType = tuihuanBuyPrice.getTradeType();
            peizhi = tuihuanBuyPrice.getPeizhi();
            piaoType = tuihuanBuyPrice.getPiaoType();

            nkuBaiTiaoPrice = yifuM.subtract(kuBaiTiaoPrice);
            if (userid != 0 && baksetId != 0 && ishuishou == 0) {
                ovgSaveMoney = shouhouTuihuanService.getOvgReturnPrice(baksetId, userid);
                if (ovgSaveMoney.compareTo(BigDecimal.ZERO) > 0) {
                    userSaveMoney = shouhouTuihuanService.getSaveMoneyByUserId(userid);
                }

            }
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(info.getAreaid());
            int tradeType1 = tradeType;
            int tradeDay1 = tradeDay;
            if (ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) {
                tuihunConfig =
                        tuihuanConfigService.getAllTuihuuanConfigByCache().stream().filter(t -> t.getKinds() == tradeType1 && t.getXtenant().equals(areaInfoR.getData().getXtenant()) && tradeDay1 >= t.getSDay() && tradeDay1 <= t.getEDay()).map(e -> {
                            TuihuanConfigRes tuihuanConfigRes = new TuihuanConfigRes();
                            BeanUtils.copyProperties(e, tuihuanConfigRes);
                            return tuihuanConfigRes;
                        }).collect(Collectors.toList());
            }
        }
        if (huan != null) {
            //获取BASKETID
            baitiaoPrice = huan.getBaitiaoM();
            coinM = huan.getCoinM();
            kuBaiTiaoPrice = huan.getKuBaiTiaoM();
            tradeType = huan.getTradeType();
            if (huan.getTradeDate() != null) {
                tradeDate = huan.getTradeDate();
                tradeDay = (int) Duration.between(tradeDate, LocalDateTime.now()).toDays();
            }
            peizhi = huan.getPeizhi();
            if (huan.getPiaoType() != null && huan.getPiaoType() != 0) {
                piaoType = huan.getPiaoType().toString();
            }
        }
        shouhouTuihuanRes.setNkuBaiTiaoPrice(nkuBaiTiaoPrice);
        shouhouTuihuanRes.setBuyPriceM(sub_buypriceM);
        shouhouTuihuanRes.setShouhouId(id);
        ShouhouTuihuanRes.ShouhouRes infoRes = new ShouhouTuihuanRes.ShouhouRes();
        BeanUtils.copyProperties(info, infoRes);
        shouhouTuihuanRes.setShouhou(infoRes);
        shouhouTuihuanRes.setCurrArea(oaUserBO.getArea());
        shouhouTuihuanRes.setAuthorizeId(oaUserBO.getAuthorizeId());
        shouhouTuihuanRes.setMyRank(oaUserBO.getRank());
        shouhouTuihuanRes.setCoinM(coinM);
        shouhouTuihuanRes.setGiftPrice(giftPrice);
        shouhouTuihuanRes.setBaitiaoPrice(baitiaoPrice);
        shouhouTuihuanRes.setKuBaiTiaoPrice(kuBaiTiaoPrice);
        shouhouTuihuanRes.setOvgSaveMoney(ovgSaveMoney);
        shouhouTuihuanRes.setUserSaveMoney(userSaveMoney);
        shouhouTuihuanRes.setTradeDate(tradeDate);
        shouhouTuihuanRes.setTradeDay(tradeDay);
        shouhouTuihuanRes.setTradeType(tradeType);
        shouhouTuihuanRes.setTuihunConfig(tuihunConfig);

        List<String> faultOptionList = this.getFaultOptionList(tuihunConfig);
        shouhouTuihuanRes.setFaultOptionList(faultOptionList);

        shouhouTuihuanRes.setPeizhi(peizhi);
        shouhouTuihuanRes.setPiaoType(piaoType);
        if (piaoType != null) {
            String piaoTypeText = EnumUtil.getMessageByCode(ShouhouPiaoTypeEnum.class, piaoType);
            piaoTypeText = StringUtils.isNotEmpty(piaoTypeText) ? "未开票" : piaoTypeText;
            shouhouTuihuanRes.setPiaoTypeText(piaoTypeText);
        }
        //获取中移金服余额
        ZhonyiRes zhonyiRes = shouhouTuihuanService.getZhonyiMoney(id);
        shouhouTuihuanRes.setZhongyiMoney(zhonyiRes == null ? BigDecimal.ZERO : zhonyiRes.getPrice());
        //获取微信红包金额
        if (baksetId != 0 || huan != null) {
            wxhongbao = this.getWeiXinHongbao(baksetId, info.getImei());
        }
        //红包金额
        shouhouTuihuanRes.setWxhongbao(wxhongbao);
        //查询是否满足普惠分区退款（支票返回）
        if (CommenUtil.isNotNullZero(info.getSubId()) && info.getBaoxiu() != 2 && info.getTradedate() != null) {
            OtherShouYinBo puhui = shouyinOtherService.getOtherShouYinBo(info.getSubId(),
                    OtherShouYinTypeEnum.PHFQ.getCode());
            OtherShouYinBo jiexin = shouyinOtherService.getOtherShouYinBo(info.getSubId(),
                    OtherShouYinTypeEnum.JXLQ.getCode());
            shouhouTuihuanRes.setPuhui(puhui);
            if (jiexin != null && huan != null && huan.getBuypriceM() != null) {
                BigDecimal kehuM1 = huan.getBuypriceM().subtract(jiexin.getPrice() == null ? BigDecimal.ZERO : jiexin.getPrice());
                kehuM1 = kehuM1.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : kehuM1;
                jiexin.setKehuM1(kehuM1);
            }
            shouhouTuihuanRes.setJiexin(jiexin);
        }
        if (huan != null && (TuihuanKindEnum.TK.getCode().equals(huan.getTuihuanKind())) && ("微信秒退".equals(huan.getTuiWay()))
                && (huan.getCheck2() != null && huan.getCheck2()) && (huan.getCheck3() == null || !huan.getCheck3())) {
            Boolean isMemberBindingWeixin = weixinUserService.isMemberBindingWeixin(info.getUserid());
            shouhouTuihuanRes.setIsMemberBindingWeixin(isMemberBindingWeixin);
            if (isMemberBindingWeixin) {
                MemberWXAuthRes memberWXAuthRes = weixinUserService.getWxAuthInfo((long) info.getSubId(), 1, 1);
                shouhouTuihuanRes.setWxAuthInfo(memberWXAuthRes);
            } else {
                String json = HttpClientUtil.get(wwwUrlSource.getBasicUrl() + "/ajaxOperate" +
                        ".aspx?act=getAlterMemberInfoQR&userid" +
                        "=" + info.getUserid());
                shouhouTuihuanRes.setBindingWeixinQrCode(json);
            }
        }
        shouhouTuihuanRes.setHuan(huan);
        return shouhouTuihuanRes;
    }

    /**
     * 保存回收库存日志
     *
     * @param mkcId
     * @param comment
     * @param inUser
     * @param showType
     */
    private void saveRecoverMkcNewLogs(Integer mkcId, String comment, String inUser, Boolean showType) {
        if (null == mkcId || 0 == mkcId || StringUtils.isEmpty(comment)) {
            return;
        }
        RecoverMkcNewLogs recoverMkcNew_logs = recoverMkcNewLogsRepository.findById(mkcId).orElse(null);
        if (recoverMkcNew_logs == null) {
            recoverMkcNew_logs = new RecoverMkcNewLogs(mkcId);
        }
        RecoverMkcNewLogs.Conts conts = new RecoverMkcNewLogs.Conts();
        conts.setComment(comment);
        conts.setDTime(LocalDateTime.now());
        conts.setInUser(inUser);
        conts.setShowType(showType);
        recoverMkcNew_logs.getCons().add(conts);
        recoverMkcNewLogsRepository.save(recoverMkcNew_logs);
    }

    @Override
    public R<Boolean> sendAddressInfo(Integer yuYueId, String smsMobile, String mobile, String addr,
                                      String rUserName, Integer addressCityIdParam) {

        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.error(ResultCode.UNLOGIN, "请先登录");
        }

        if (yuYueId == null) {
            return R.error("预约单号为空，请先生成预约单后再操作邮寄地址发送");
        }
        Integer addressCityid = ObjectUtil.defaultIfNull(addressCityIdParam,0);
        String webName = sysConfigService.getWebNameByXtenant(oaUserBO.getXTenant());
        String tipMsg = "（温馨提示：若您自行寄件可优先选择顺丰到付，我司收到设备会及时与您联系，建议您关注并登录" + webName + "微信公众号，方便您随时查看处理进度，如有疑问，可咨询客服************。";
        if (StringUtils.isNotEmpty(addr) && yuYueId > 0 && StringUtils.isNotEmpty(mobile)) {

            String sendMsg = String.format("邮寄地址：%s，收件人：%s，电话：%s",addr , rUserName, mobile);

            R<Boolean> response = smsService.sendSms(smsMobile, sendMsg + tipMsg, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(oaUserBO.getAreaId(), ESmsChannelTypeEnum.YZMTD));

            if (response.getCode() == ResultCode.RETURN_ERROR) {
                return R.error("消息发送失败");
            } else {
                //添加地址发送记录 shouhou_yuyue
                ShouhouSendaddress shouhouSendaddress = new ShouhouSendaddress();
                shouhouSendaddress.setLinkid(yuYueId);
                shouhouSendaddress.setKind(DealWayEnum.WX.getCode());
                shouhouSendaddress.setAddress(addr);
                shouhouSendaddress.setCityid(addressCityid);
                shouhouSendaddress.setRecover(rUserName);
                shouhouSendaddress.setPhone(mobile);
                addShouhouSendAddress(shouhouSendaddress, oaUserBO.getUserName());
                return R.success("发送成功");
            }

        } else {
            return R.error(ResultCode.PARAM_ERROR, "请求参数有误");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<ShouhouCostPriceRes> addCostPriceWithAutoBind(WxFeeBo m, Boolean autoHuishou, Boolean isLockKc){
        ShouhouService shouhouService = SpringContextUtil.getContext().getBean(ShouhouService.class);
        //逻辑修正 如果ppid 为0 不存在锁定库存 xxk
        if(Boolean.TRUE.equals(isLockKc) && CommenUtil.isNullOrZero(m.getPpid())){
            isLockKc = Boolean.FALSE;
        }
        R<ShouhouCostPriceRes> shcpRes = shouhouService.addCostPrice(m, autoHuishou, isLockKc);
        if(shcpRes.getCode() == ResultCode.SUCCESS){
            //解锁收银lock
            baseMapper.unShouYinLock(m.getShouhouId());
            //处理绑定配件
            SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.IS_SHOUHOU_BIND_PEIJIAN, true));
            handlerBindPeiJian(m, isLockKc, shouhouService, shcpRes);
        }else{
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return shcpRes;
    }

    private void handlerBindPeiJian(WxFeeBo m, Boolean isLockKc, ShouhouService shouhouService, R<ShouhouCostPriceRes> shcpRes) {
        //获取绑定配件的库存信息 放入返回结果
        List<BindPpidInfoBo> bindPpidInfoBos = this.getBindPpidKcInfo(m.getPpid(), m.getXtenant()).stream()
                .map(BindPpidKcInfo::toBindPpidInfo).collect(Collectors.toList());
        m.setBindPpidInfos(bindPpidInfoBos);
        shcpRes.getData().setWxFeeBo(m);
        //获取自动出库的配件信息 兼容旧代码
        List<BindPpidInfoBo> bpiList = Optional.ofNullable(XtenantEnum.isJiujiXtenant(m.getXtenant()))
                .filter(Boolean::booleanValue)
                .map(jiuji-> shouhouPpidBindService.listBindPpidInfo(m.getPpid(), true, m.getXtenant(), m.getAreaId()))
                .filter(CollectionUtils::isNotEmpty)
                //转换为查询对象
                .map(bpis->bpis.stream().map(bp->BindPpidInfoBo.of(bp.getId(),bp.getPpid(),1)).collect(Collectors.toList()))
                //兼容旧查询方式
                .orElseGet(()->
                    shouhouPpidDictService.getAllPpidDictCache().stream()
                            .filter(e -> Optional.ofNullable(e.getPpidList())
                                    .orElseGet(()->Arrays.asList(e.getPpids().split(SignConstant.COMMA))).contains(m.getPpid().toString()))
                            .map(e->Collections.singletonList(BindPpidInfoBo.of(-1,e.getPpid(),1)))
                            .findFirst().orElseGet(Collections::emptyList)
                );
        //绑定自动出库配件为空,直接返回
        if(bpiList.isEmpty()){
            return;
        }
        //自动出库,库存操作日志: 加上配件id,配置id
        WxFeeBo feeBo = m.clone();
        feeBo.setBindPpidInfos(bpiList);
        R<List<ShouhouCostPriceRes>> scpRes = shouhouService.addBindCostPrice(feeBo,true, isLockKc,false);
        //将返回结果拼接 每个配件的返回结果都带上ppid
        shcpRes.setUserMsg(String.format("%s,绑定配件: %s", shcpRes.getUserMsg(),scpRes.getUserMsg()));
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public R<ShouhouCostPriceRes> addCostPrice(WxFeeBo m, Boolean autoHuishou, Boolean isLockKc) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (autoHuishou == null) {
            autoHuishou = false;
        }
        if (isLockKc == null) {
            isLockKc = true;
        }

        List<Integer> shouhouServicesPpriceids = shouhouConstants.getShouhouServicesPpriceids();
        Boolean shouhouServicesFlag = shouhouServicesPpriceids.contains(m.getPpid()) ? true : false;
        Shouhou shouhou = baseMapper.selectById(m.getShouhouId());
        if (shouhou == null) {
            return R.error("维修记录不存在");
        }
        Optional.ofNullable(m).ifPresent(wxFeeBo->wxFeeBo.setIshuishou(shouhou.getIshuishou()));
        Integer nowarea = shouhou.getToareaid() != null ? shouhou.getToareaid() : shouhou.getAreaid();
        if (nowarea == null || !nowarea.equals(m.getAreaId())) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(nowarea);
            String area = "";
            if (ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) {
                area = areaInfoR.getData().getArea();
            }
            return R.error("地区不对，请先切换至" + area + "再入成本！");
        }
        if (shouhou.getIsquji() != null && shouhou.getIsquji() && CommenUtil.isNotNullZero(shouhou.getYuyueid())) {
            return R.error("已经取机，不可再入成本！");
        } else if ((shouhou.getYuyueid() == null || shouhou.getYuyueid() != -1) && Boolean.TRUE.equals(shouhou.getIsquji())) {
            return R.error("已取机已收银不可操作！");
        }
//        if (!shouhouServicesFlag) {
//            if (shouhou.getIsquji() != null && shouhou.getIsquji() && CommenUtil.isNotNullZero(shouhou.getYuyueid())) {
//                return R.error("已经取机，不可再入成本！");
//            } else if ((shouhou.getYuyueid() == null || shouhou.getYuyueid() != -1) && Boolean.TRUE.equals(shouhou.getIsquji())) {
//                return R.error("已取机已收银不可操作！");
//            }
//        } else {
////            if (shouhou.getImei() == null || shouhou.getImei().length() != 15) {
////                return R.error("无效串号不能绑定！");
////            }
//
//            Duration d = Duration.between(shouhou.getOfftime() == null ? LocalDateTime.MIN : shouhou.getOfftime(), LocalDateTime.now());
//            if (Boolean.TRUE.equals(shouhou.getIsquji()) && d.toDays() > 0) {
//                return R.error("售后服务仅限取机当天购买！");
//            }
//            if (!wxkcoutputService.checkExistShouhouService(m.getPpid(), m.getShouhouId())) {
//                return R.error("请勿重复添加售后服务！");
//            }
//        }
        Boolean priceChangeFlag = false;

        Integer servicesType = shouhou.getServiceType() == null ? 0 : shouhou.getServiceType();
        if (shouhouConstants.getShouhouServiceTypeList().contains(servicesType)) {
            //获取配件对应服务类别
            List<Integer> serviceTypes = DecideUtil.iif(XtenantEnum.isJiujiXtenant(),()->shouhouServiceConfigService.getShouhouServiceConfigs(Collections.singletonList(m.getPpid()))
                    .getData().stream().map(ShouhouServiceConfig::getServiceType).distinct().collect(Collectors.toList()),
                    ()->Optional.ofNullable(shouhouServiceConfigService.getShouhouServicesTypeByPpid(m.getPpid(), oaUserBO.getXTenant()))
                            .map(st->Collections.singletonList(st)).orElse((List<Integer>)Collections.EMPTY_LIST));
            if (serviceTypes.contains(servicesType)) {
                if (servicesType.equals(BaoXiuTypeEnum.SHPMB.getCode())) {
                    m.setPrice(m.getPrice1().multiply(BigDecimal.valueOf(0.5)));
                    priceChangeFlag = true;
                } else if (servicesType.equals(BaoXiuTypeEnum.SHDCB.getCode())) {
                    m.setPrice(BigDecimal.ZERO);
                    priceChangeFlag = true;
                } else if (servicesType.equals(BaoXiuTypeEnum.SHHGX.getCode())) {
                    m.setPrice(m.getPrice1().multiply(BigDecimal.valueOf(0.5)));
                    priceChangeFlag = true;
                }
            }
        }


        String fuwuConfig = "";
        List<ShouhouServiceConfig> fuwuConfigList = DecideUtil.iif(XtenantEnum.isJiujiXtenant(),
                ()->shouhouServiceConfigService.getShouhouServiceConfigs(Optional.ofNullable(shouhou.getProductId()).orElse(0L),Arrays.asList(m.getPpid())).getData(),
                ()->shouhouServiceConfigService.getShouhouServicesList(Arrays.asList(m.getPpid())));
        if (CollectionUtils.isNotEmpty(fuwuConfigList)) {
            fuwuConfig = JSON.toJSONString(fuwuConfigList);
        }
        Boolean isHuan = shouhou.getWxkind() != null && 5 == shouhou.getWxkind();
        //如果是置换总成，则抵扣成本
        if (isHuan) {
            autoHuishou = true;
        }

        //查询ppids信息
        List<ShouHouPpidDict> ppidDictList = shouhouPpidDictService.getAllPpidDictCache();
        //获取置换价格
        BigDecimal pjPrice = m.getPrice();
        if (!priceChangeFlag) {
            pjPrice = shoushouPjpriceConfigService.getPjPrice(m.getPpid(), m.getPrice(), autoHuishou);
        }

        R<Boolean> result = shouhouService.pjck(m, autoHuishou, isLockKc, shouhou, priceChangeFlag, shouhouServicesFlag, ppidDictList, pjPrice);

        if (ResultCode.SUCCESS == result.getCode()) {
             if(priceChangeFlag) {
                String comments = "维修配件【" + m.getProductName() + "， PPID:" + m.getPpid() + "】 价格由：" + m.getPrice1() + "改为：" + m.getPrice();
                this.saveShouhouLog(m.getShouhouId(), comments, "系统");
            }
            ShouhouCostPriceRes res = new ShouhouCostPriceRes();
            res.setShouhoufuwu(1);
            res.setFuwuConfig(fuwuConfig);
            //todo 这里如果服务config不为空，则调用网站接口获取服务介绍 2021-03-22 需求
            if (StringUtils.isNotEmpty(fuwuConfig)) {

//                    String host = sysConfigService.getValueByCode(SysConfigConstant.WEB_URL);
//                    if (StringUtils.isNotEmpty(host)) {
////                        host = "https://www.dev.9ji.com";
//                        String url = host + "/web/api/oa/getEditionUrl?ppids=" + fuwuConfigList.get(0).getServicePpid();
//                        String jsonStr = HttpUtil.get(url);
//                        if (StringUtils.isNotEmpty(jsonStr)) {
//                            R<List<ShouhouServiceDescriptionInfo>> r = JSONObject.parseObject(jsonStr, R.class);
//                            List<ShouhouServiceDescriptionInfo> dataList = r.getData();
//                            JSONArray jsonArray = JSONObject.parseObject(jsonStr).getJSONArray("data");
//                            ShouhouServiceDescriptionInfo itemList = jsonArray.getObject(0, ShouhouServiceDescriptionInfo.class);
//                            if (CollectionUtils.isNotEmpty(dataList) && CollectionUtils.isNotEmpty(itemList.getInfo())){
//                                ShouhouServiceDescriptionInfoItem info = itemList.getInfo().get(0);
//                                res.setUrl(info.getUrl());
//                                res.setDescription(info.getName());
//                            }
//                        }
//                    }
                if (XtenantEnum.isJiujiXtenant()) {
                    fuwuConfigList.stream().findFirst()
                            .ifPresent(fConfig -> {
                                res.setUrl(fConfig.getUrl());
                                res.setDescription(fConfig.getDescription());
                                res.setProductColor(fConfig.getProductColor());
                            });

                }else{
                    String host = sysConfigService.getValueByCode(SysConfigConstant.M_URL);
                    Integer serviceType = fuwuConfigList.get(0).getServiceType();
                    ServiceTypeEnum serviceEnum = EnumUtil.getEnumByCode(ServiceTypeEnum.class, serviceType);
                    String url = host + serviceEnum.getUrl();
                    res.setUrl(url);
                    res.setDescription(serviceEnum.getMessage() + "服务介绍");
                    List<Productinfo> list = productinfoService.list(new LambdaQueryWrapper<Productinfo>().eq(Productinfo::getPpriceid, fuwuConfigList.get(0).getServicePpid()));
                    if (CollectionUtils.isNotEmpty(list)) {
                        res.setProductColor(list.get(0).getProductColor());
                    }
                }


            }


            return R.success(result.getUserMsg(), res);
        }
        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        return R.error(result.getUserMsg());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> pjck(WxFeeBo m, Boolean autoHuishou, Boolean isLockKc, Shouhou shouhou, Boolean priceChangeFlag
            , Boolean shouhouServicesFlag, List<ShouHouPpidDict> ppidDictList, BigDecimal pjPrice) {
        try {

//            //添加门店剩余可用库存校验逻辑
//            if (CommenUtil.isNotNullZero(m.getPpid()) && !Arrays.asList(81682,81683).contains(m.getPpid())){
//                List<ProductKc> productKcList = productKcService.list(new LambdaQueryWrapper<ProductKc>().eq(ProductKc::getPpriceid,m.getPpid()).eq(ProductKc::getAreaid,m.getAreaId()));
//                if(CollectionUtils.isNotEmpty(productKcList)){
//                    ProductKc productKc = productKcList.get(0);
//                    Integer leftCount = productKc.getLeftCount() == null ? 0 : productKc.getLeftCount();
//                    if (leftCount <= 0 ){
//                        return R.error("配件库存数量不足，请刷新页面后再试");
//                    }
//                }
//
//            }

            Wxkcoutput wxkcoutput = buildWxkcoutput(m, isLockKc, pjPrice);
            //增加会员折扣 xxk
            R<DiscountInfoBo> discountR = tryUseMemberDiscount(m, wxkcoutput);
            //如果是九机并且是回收增值订单 添加维修配件的价格系统自动改为成本价
            if(XtenantEnum.isJiujiXtenant() && IshuishouEnum.VALUE_ADDED_RECYCLING.getCode().equals(m.getIshuishou())){
                List<Integer> repairServiceCids = SpringUtil.getBean(CategoryService.class).selectCategoryChildrenByCid(Collections.singletonList(REPAIR_SERVICE_CID));
                Integer cid = Optional.ofNullable(productinfoService.getProductinfoByPpid(m.getPpid())).orElse(new Productinfo()).getCid();
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "回收增值机, 排除分类: {}, 当前配件分类: {}", repairServiceCids, cid);
                if(!repairServiceCids.contains(cid)){
                    BigDecimal inprice = wxkcoutput.getInprice();
                    String logComment = StrUtil.format("回收增值机, 维修配件【{}， PPID:{}】 价格由：{} 改为：{}",
                            m.getProductName(), m.getPpid(), wxkcoutput.getPrice(), inprice);
                    SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, logComment);
                    this.saveShouhouLog(m.getShouhouId(), logComment, "系统");
                    wxkcoutput.setPrice(inprice);
                    wxkcoutput.setPrice1(inprice);
                }
            }
            Boolean b = wxkcoutputService.saveWxkcoutput(wxkcoutput);
            m.setId(wxkcoutput.getId());
//
//            //添加的维修库存如果是九机服务,将对应的服务类型更新至售后表中
//            if (Arrays.asList(81682,81683).contains(m.getPpid())){
//                Integer serviceType = m.getPpid().equals(81682) ? 9 : 10;
//                super.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getServiceType,serviceType).eq(Shouhou::getId,m.getShouhouId()));
//            }
            if (!b) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("更新数据库失败！");
            }

            //1表示仅仅添加维修费，2表示添加维修配件,2才涉及到出库。
            Boolean kcStats = shouhouService.handlePjck(m, isLockKc, shouhou);

            if (!kcStats) {
                //操作库存失败
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("出库失败，库存不足！");
            }

            if (CommenUtil.isCheckTrue(m.getIshexiao())) {
                shouhouHexiaoService.saveShouhouHexiao(wxkcoutput.getId(), 1);
            }
            //回收旧件
            if (autoHuishou) {
                ShouhouHuishou shouhouHuishou = buildShouhouHuishou(m, wxkcoutput);
                shouhouHuishouService.saveShouhouHuishou(shouhouHuishou);
            }
            R<Boolean> result = pjckUpdateFeiyong(m, autoHuishou, shouhou, pjPrice);
            if(!discountR.isSuccess()){
                result.addBusinessLog(discountR.getUserMsg());
            }

            return result;
        } catch (Exception e) {
//            e.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("出库失败，不可预见的错误", e);
        }
        return R.error("出库失败");
    }

    private R<DiscountInfoBo> tryUseMemberDiscount(WxFeeBo m, Wxkcoutput wxkcoutput) {
        UseDiscountBo useDiscount = UseDiscountBo.wrapper(m);
        R<DiscountInfoBo> discountR = memberDiscountService.tryUseDiscount(useDiscount);
        if(!discountR.isSuccess()){
            return discountR;
        }
        wxkcoutput.setMemberDiscountAmount(useDiscount.getDiscountAmount());
        if(useDiscount.getPriceTypeEnum() == UseDiscountBo.PriceTypeEnum.PJ){
            wxkcoutput.setPrice(useDiscount.getPrice());
        }else{
            wxkcoutput.setPriceGs(useDiscount.getPrice());
        }
        return discountR;
    }

    private R<Boolean> pjckUpdateFeiyong(WxFeeBo m, Boolean autoHuishou, Shouhou shouhou, BigDecimal pjPrice) {
        //更新售后费用
        repairService.updateShouhouFeiyong(shouhou.getId());
        ShouhouFeiyongBo shouhouFeiyongBo = null;
        Shouhou shouhou1 = this.getById(m.getShouhouId());
        if (shouhou1 != null) {
            shouhouFeiyongBo = new ShouhouFeiyongBo();
            shouhouFeiyongBo.setCostprice(shouhou1.getCostprice());
            shouhouFeiyongBo.setFeiyong(shouhou1.getFeiyong());
        }
        if (shouhouFeiyongBo == null) {
            return R.error("数据已被修改");
        }
        //正式环境，rabbitMQ推送(要放在出其他配件逻辑前面，不然ppid会被改掉
        Map<String, String> data = new HashMap<>();
        data.put("areaid", String.valueOf(m.getAreaId()));
        data.put("ppriceid", String.valueOf(Optional.ofNullable(m.getPpid()).orElse(NumberConstant.ZERO)));
        OaQuequRes oaQuequRes = new OaQuequRes("prodcutKcOutWxpj", data);
        smsService.oaRabbitMQWorkQueue(JSON.toJSONString(oaQuequRes), RabbitMqConfig.QUEUE_TOPIC_OAASYNC);
        if (autoHuishou && pjPrice != null && !pjPrice.equals(m.getPrice())) {
            String logMsg = "置换业务价格调整: 价格由" + m.getPrice() + "变为" + pjPrice;
            saveShouhouLog(m.getShouhouId(), logMsg, m.getUser(), null, false);
        }
        String prevLog = StrUtil.isBlank(m.getPrevLog()) ? "" : m.getPrevLog();
        if (m.getPpid() != null && m.getPpid() > 0) {
            //发送关注通知
            shouhouMsgService.sendCollectMsg(m.getShouhouId(), "增加维修配件【" + m.getProductName() + "，PPID:" + m.getPpid() + "】", m.getUser());
            saveShouhouLog(m.getShouhouId(), prevLog + "增加维修配件【" + m.getProductName() + "，PPID:" + m.getPpid() + "】价格:" + m.getPrice(), m.getUser(), null, false);
        } else {
            saveShouhouLog(m.getShouhouId(), prevLog + "增加费用【" + m.getProductName() + "】价格:" + m.getPrice(), m.getUser(), null, false);
        }
        //附带出库其他配件逻辑
        /*if (CommenUtil.isNotNullZero(m.getPpid())) {
            addOtherCostPriceByPPriceid(m, ppidDictList);
        }*/
        return R.success(shouhouFeiyongBo.getCostprice() + "|" + shouhouFeiyongBo.getFeiyong(), true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handlePjck(WxFeeBo m, Boolean isLockKc, Shouhou shouhou) {
        Boolean kcStats = true;
        if (m.getKinds().equals(2)) {
            if (isLockKc) {
                //锁定库存
                Boolean flag = productKcService.lockKc(m.getPpid(), m.getAreaId());
                if(!flag){
                    throw new CustomizeException(String.format("库存不足, 锁定异常, 请核对库存量ppid: %s,areaId:%s",m.getPpid(),m.getAreaId()));
                }
            } else {
                //库存操作 new apiServices().product_kc
                OperateProductKcPara para = buildOperateProductKcPara(m);

                R<OperateProductKcRes> productKcR = productKcService.operateProductKc(para);

//                    SmallproNormalCodeMessageRes codeMessageRes = smallproForwardExService.stockOperations(m.getPpid(), -1, m.getPrice(), m.getAreaId(), m.getUser(), "", "维修出库,售后单:" + m.getShouhouId(), null, 0, 0, m.getShouhouId(), false, false);
                if (productKcR.getCode() != ResultCode.SUCCESS) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    kcStats = false;
                } else {
                    ShouhouApply shouhouApply = shouhouApplyService.getShouhouApply(m.getPpid(), m.getShouhouId());
                    if (shouhouApply != null) {
                        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(shouhou.getAreaid());
                        String printName = "";
                        if (ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) {
                            AreaInfo areaInfo = areaInfoR.getData();
                            printName = areaInfo.getPrintName();
                        }
                        String title = DecideUtil.iif(StringUtils.isEmpty(shouhou.getSxname()),""
                                ,DecideUtil.iif(shouhou.getSxname().contains("先生") || shouhou.getSxname().contains("女士"),""
                                        ,DecideUtil.iif(1 == shouhou.getSxsex() , "先生" , "女士")));
                        String message = "尊敬的" + printName + "会员 " + shouhou.getSxname() + title + "您好，您的产品售后问题订购的配件已经到货，正在由维修工程师" + m.getUser() + "为您进行维修处理，感谢您的耐心等待！";
                        shouhouService.saveShouhouLog(shouhou.getId(), message, m.getUser(), null, true);
                    }
                }
            }
        }
        return kcStats;
    }

    private static ShouhouHuishou buildShouhouHuishou(WxFeeBo m, Wxkcoutput wxkcoutput) {
        ShouhouHuishou shouhouHuishou = new ShouhouHuishou();
        shouhouHuishou.setShouhouId(m.getShouhouId());
        shouhouHuishou.setPpid(m.getPpid());
        shouhouHuishou.setWxkcid(wxkcoutput.getId());
        shouhouHuishou.setAreaid(m.getAreaId());
        shouhouHuishou.setName(m.getProductName());
        shouhouHuishou.setInuser(m.getUser());
        shouhouHuishou.setIsfan(false);
        return shouhouHuishou;
    }

    private static OperateProductKcPara buildOperateProductKcPara(WxFeeBo m) {
        OperateProductKcPara para = new OperateProductKcPara();
        para.setPpid(m.getPpid());
        para.setCount(-1);
        para.setInprice(m.getPrice());
        para.setAreaId(m.getAreaId());
        para.setInuser(m.getUser());
        para.setInsource("");
        para.setComment(DecideUtil.iif(StringUtils.isEmpty(m.getOperateProductKcLog()) , "维修出库,售后单:" + m.getShouhouId() , m.getOperateProductKcLog()));
        para.setBasketId(null);
        para.setCheck1(false);
        para.setCheck2(false);
        para.setShouhouId(Long.valueOf(m.getShouhouId()));
        para.setIsLp(false);
        para.setDiaoboFlag(false);
        return para;
    }

    private static Wxkcoutput buildWxkcoutput(WxFeeBo m, Boolean isLockKc, BigDecimal pjPrice) {
        Wxkcoutput wxkcoutput = new Wxkcoutput();
        wxkcoutput.setWxid(m.getShouhouId());
        wxkcoutput.setName(m.getProductName());
        wxkcoutput.setInuser(m.getUser());
        wxkcoutput.setServiceRecordId(m.getServiceRecordId());
        wxkcoutput.setDtime(LocalDateTime.now());
        wxkcoutput.setAreaid(m.getAreaId());
        wxkcoutput.setPrice(Optional.ofNullable(pjPrice).orElse(BigDecimal.ZERO));
        wxkcoutput.setPrice1(Optional.ofNullable(m.getPrice1()).orElse(BigDecimal.ZERO));
        wxkcoutput.setPriceGs(Optional.ofNullable(m.getPriceGs()).orElse(BigDecimal.ZERO));
        wxkcoutput.setInprice(Optional.ofNullable(m.getInprice()).orElse(BigDecimal.ZERO));
        wxkcoutput.setPpriceid(m.getPpid());
        wxkcoutput.setIslockkc(Boolean.TRUE.equals(isLockKc));
        wxkcoutput.setIsyouhuima(m.getIsyouhuima() != null && m.getIsyouhuima());
        wxkcoutput.setStats(0);
        wxkcoutput.setBindId(m.getBindId());
        wxkcoutput.setOutputDtime(DecideUtil.iif(CommenUtil.isNullOrZero(m.getPpid()) || !Boolean.TRUE.equals(isLockKc)
                ,LocalDateTime.now(),null));
        return wxkcoutput;
    }


    /**
     * 添加预约地址发送记录
     *
     * @param shouhouSendaddress
     * @param currentUserName
     * @return
     */
    private boolean addShouhouSendAddress(ShouhouSendaddress shouhouSendaddress, String currentUserName) {
        boolean isRecord = false;
        if (shouhouSendaddress.getKind().equals(DealWayEnum.WX.getCode())) {
            ShouhouYuyue shouhouYuyue = shouhouYuyueService.getById(shouhouSendaddress.getLinkid());
            if (shouhouYuyue == null) {
                return false;
            }
            //邮寄送修预约的的才记录
            Integer stype = shouhouYuyue.getStype();
            if (stype.equals(ServicesWayEnum.YJSX.getCode())) {
                isRecord = true;
                //  记录日志
                String comment = String.format(
                        "邮寄送修地址发送：%s，收件人：%s，电话：%s",
                        shouhouSendaddress.getAddress(),
                        shouhouSendaddress.getRecover(),
                        shouhouSendaddress.getPhone()
                );
                saveYuYueLogs(shouhouSendaddress.getLinkid(), comment, currentUserName, 0);

            }
        }
        if (isRecord) {
            shouhouSendaddress.setInuser(currentUserName);
            shouhouSendaddress.setDtime(LocalDateTime.now());
            shouhouSendaddressService.save(shouhouSendaddress);
        }
        return true;
    }

    private boolean saveYuYueLogs(Integer yuYueId, String comment, String inuser, Integer viewType) {
        YuyueLogs yuyueLogs = new YuyueLogs();
        yuyueLogs.setYuyueId(yuYueId);
        yuyueLogs.setComment(comment);
        yuyueLogs.setInuser(inuser);
        yuyueLogs.setDtime(LocalDateTime.now());
        yuyueLogs.setViewType(viewType);
        return yuyueLogsService.save(yuyueLogs);

    }

    @Override
    public String editPriceByIshuanhuo(ShouhouHuiShouReq req) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        Wxkcoutput wxkcoutput = wxkcoutputService.getById(req.getWxkcId());
        if (Objects.isNull(wxkcoutput)) {
            return "维修配件异常，请刷新后重试！";
        }
        Shouhou shouhou = baseMapper.selectById(req.getShouhouId());
        if (StrUtil.isNotEmpty(shouhou.getYouhuima())) {
            Integer huanCount = shouhouHuishouService.count(new LambdaQueryWrapper<ShouhouHuishou>()
                    .eq(ShouhouHuishou::getShouhouId, shouhou.getId()).and(bo -> bo.eq(ShouhouHuishou::getIshuan, 1).or().isNull(ShouhouHuishou::getIshuan)));
            if (shouhou.getYouhuifeiyong() != null && shouhou.getYouhuifeiyong().compareTo(BigDecimal.ZERO) > 0 && huanCount.equals(0) && CommenUtil.isNullOrZero(shouhou.getServiceType())) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return "此单已使用优惠码，不允许改价！";
            }
        }
        //记录日志
        if (wxkcoutput.getPrice().compareTo(BigDecimal.ZERO) != 0) {
            String comment = "换货操作，维修库存ID:" + wxkcoutput.getId() + " 价格由" + wxkcoutput.getPrice() + "更改为0";
            saveShouhouLog(req.getShouhouId(), comment, oaUserBO.getUserName(), null, false);
        }
        //设置维修配件价格为0
        boolean isUpdate = wxkcoutputService.update(new LambdaUpdateWrapper<Wxkcoutput>().set(Wxkcoutput::getPrice, BigDecimal.ZERO).eq(Wxkcoutput::getId, wxkcoutput.getId()));
        //更新售后费用
        if(isUpdate){
            repairService.updateShouhouFeiyong(shouhou.getId());
        }

        return null;
    }

    //维修配件改价
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> editPrice(ShouhouDtBo sh, List<Integer> kcOutId,
                                List<BigDecimal> kcPrice, List<BigDecimal> kcInprice, List<BigDecimal> kcPriceGs, List<String> myRank,
                                String user, Integer areaKind1, String showKind, LambdaUpdateWrapper<Shouhou> updateWrapper,
                                List<ShouhouLogNewBo> shLogs, boolean isJiujiServiceFlag) {

        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        Integer shouhouId = sh.getId();
        BigDecimal rsFeiyong = sh.getFeiyong() == null ? BigDecimal.ZERO : sh.getFeiyong();
        BigDecimal rsCostprice = sh.getCostprice() == null ? BigDecimal.ZERO : sh.getCostprice();

        R<List<AreaInfo>> areaAllInfoR = areaInfoClient.listAll();
        if (areaAllInfoR.getCode() != ResultCode.SUCCESS || CollectionUtils.isEmpty(areaAllInfoR.getData())) {
            return R.error("获取门店信息出错！");
        }
        //湖南分公司加盟体系ID
        List<AreaInfo> hnAreaInfoList = areaAllInfoR.getData().stream().filter(p -> Arrays.asList(46, 54, 56, 57, 59).contains(p.getAuthorizeId())).collect(Collectors.toList());

        List<Integer> hnArea = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(hnAreaInfoList)) {
            hnArea = hnAreaInfoList.stream().map(s -> s.getId()).collect(Collectors.toList());
        }

        //额外开放改低维修费的ID(C区)
        hnArea.add(20);
        hnArea.add(152);
        hnArea.add(174);
        hnArea.add(179);
        hnArea.add(184);
        hnArea.add(186);
        hnArea.add(206);
        hnArea.add(253);

        Boolean isJiuji = oaUserBO.getXTenant().equals(0);

        BigDecimal oldKcPrice = BigDecimal.ZERO;//基础合计费用
        BigDecimal newKcPrice = BigDecimal.ZERO;//新合计费用


        List<Wxkcoutput> kcdt = wxkcoutputService.list(new QueryWrapper<Wxkcoutput>().lambda().in(kcOutId != null, Wxkcoutput::getId, kcOutId).orderByAsc(Wxkcoutput::getId));
        if (kcdt.size() != kcOutId.size()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("数据已被修改请刷新再试！");
        }

        Integer nowArea = sh.getNowArea() == null ? 0 : sh.getNowArea();
        AreaInfo nowAreaInfo = null;
        R<AreaInfo> nowAreaInfoR = areaInfoClient.getAreaInfoById(nowArea);
        if (nowAreaInfoR.getCode() == ResultCode.SUCCESS && nowAreaInfoR.getData() != null) {
            nowAreaInfo = nowAreaInfoR.getData();
        }

        Boolean isEditPrice = false;
        Integer ri = 0;

        //改价前先校验额度
        for (Wxkcoutput e : kcdt) {
            BigDecimal price = e.getPrice();
            BigDecimal price1 = e.getPrice1();
            BigDecimal priceGs = e.getPriceGs();

            oldKcPrice = oldKcPrice.add(price1).add(priceGs);
            if (price.compareTo(kcPrice.get(ri)) != 0) {
                isEditPrice = true;
                newKcPrice = newKcPrice.add(kcPrice.get(ri)).add(kcPriceGs.get(ri));
            } else {
                newKcPrice = newKcPrice.add(price).add(priceGs);
            }
            ri++;
        }
        ri = 0;
        //优惠额度逻辑
        if ((CommenUtil.isNullOrZero(sh.getServiceType()) && isEditPrice ) && !isJiujiServiceFlag) {
            BigDecimal ed = BigDecimal.ZERO;
            //改价固定值
            boolean isFixedAmount = false;
            BigDecimal ratio  = null;
            //获取可修改额度百分比
            if (myRank.contains("6g3")) {
                ed = BigDecimal.valueOf(1);
            } else if (XtenantEnum.isJiujiXtenant() && myRank.contains("6g2")) {
                isFixedAmount = true;
                ed = BigDecimal.valueOf(100);
                ratio = new BigDecimal("0.5");
            } else if (XtenantEnum.isJiujiXtenant() && myRank.contains("6g1")) {
                isFixedAmount = true;
                ed = BigDecimal.valueOf(50);
                ratio =new BigDecimal("0.2");
            } else if (myRank.contains("6g2")) {
                ed = BigDecimal.valueOf(0.5);
            } else if (myRank.contains("6g1")) {
                ed = BigDecimal.valueOf(0.2);
            }

            //自营除了湖南分公司不限制 YY网限制  输出限制
            boolean isLimitEditPrice = (isJiuji && !hnArea.contains(nowArea)) || nowAreaInfo.getAuthorizeId().equals(59) || !XtenantEnum.isJiujiXtenant();
            if (isLimitEditPrice && ed.compareTo(BigDecimal.ZERO) == 0) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("您没有改价权限！");
            }

            //修改后可优惠额度
            BigDecimal totalProfit = oldKcPrice.subtract(sh.getCostprice());
            final BigDecimal edFinal = ed;
            BigDecimal edM = DecideUtil.iif(isFixedAmount,() -> NumberUtil.min(edFinal,totalProfit),() -> totalProfit.multiply(edFinal));
            if(XtenantEnum.isJiujiXtenant() && ObjectUtil.isNotNull(ratio)){
                edM = NumberUtil.min(oldKcPrice.multiply(ratio),ed);
            }
            //如果新的价格，超过可优惠额度，则不允许使用。
            if (isLimitEditPrice && (oldKcPrice.subtract(newKcPrice)).compareTo(edM) > 0 && ed.compareTo(BigDecimal.valueOf(1)) != 0) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("已超过您可优惠的额度，您的优惠额度为：" + edM);
            }
        }

        if (isEditPrice) {
            Integer huanCount = shouhouHuishouService.count(new LambdaQueryWrapper<ShouhouHuishou>()
                    .eq(ShouhouHuishou::getShouhouId, sh.getId()).and(bo -> bo.eq(ShouhouHuishou::getIshuan, 1).or().isNull(ShouhouHuishou::getIshuan)));

            if (sh.getYouhuifeiyong() != null && sh.getYouhuifeiyong().compareTo(BigDecimal.ZERO) > 0 && huanCount.equals(0) && CommenUtil.isNullOrZero(sh.getServiceType())) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("此单已使用优惠码，不允许改价！");
            }
        }


        for (Wxkcoutput e : kcdt) {
            BigDecimal price = e.getPrice();
            BigDecimal priceGs = e.getPriceGs();

            LambdaUpdateWrapper<Wxkcoutput> wxkcoutputUpdateWrapper = new LambdaUpdateWrapper();
            Integer wxkcId = kcOutId.get(ri);
            boolean isSetPriceAndPriceGs = false;
            if (CommenUtil.isNullOrZero(sh.getShouyinglock()) && sh.getIsQuJiE().equals(0)) {
                if (!areaKind1.equals(1) && (price.compareTo(kcPrice.get(0)) > 0 || priceGs.compareTo(kcPriceGs.get(0)) > 0) && !hnArea.contains(nowArea) && !"彭勇".equals(user)
                && ObjectUtil.defaultIfNull(e.getServiceType(),0)<1) {
                    //非服务出险配件才进行验证
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return R.error("加盟店维修费不可改低！");
                }

                if (price.compareTo(kcPrice.get(ri)) != 0 || priceGs.compareTo(kcPriceGs.get(ri)) != 0) {
                    if (kcPrice.get(ri).compareTo(BigDecimal.ZERO) < 0 || kcPriceGs.get(0).compareTo(BigDecimal.ZERO) < 0) {

                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return R.error("价格均不可为负数！");
                    }

                    rsFeiyong = rsFeiyong.add(kcPrice.get(ri).subtract(price)).add(kcPriceGs.get(ri).subtract(priceGs));
                    newKcPrice = newKcPrice.add(kcPrice.get(ri)).add(kcPriceGs.get(ri));//如果改过，加表单价格
                    if (price.compareTo(kcPrice.get(ri)) != 0) {
                        String comment = "· id:" + wxkcId + " 价格由" + price + "更改为" + kcPrice.get(ri);
                        shLogs.add(new ShouhouLogNewBo(shouhouId, comment, user, null, false));
                    }
                    if (priceGs.compareTo(kcPriceGs.get(ri)) != 0) {
                        String comment = "· id:" + wxkcId + " 工时费由" + priceGs + "更改为" + kcPriceGs.get(ri);
                        shLogs.add(new ShouhouLogNewBo(shouhouId, comment, user, null, false));
                    }
                    isSetPriceAndPriceGs = true;
                }
            }
            BigDecimal inprice = e.getInprice();
            if (CommenUtil.isNullOrZero(e.getStats()) && CommenUtil.isNullOrZero(e.getPpriceid())) {
                if (inprice.compareTo(kcPrice.get(ri)) != 0) {
                    if (kcInprice.get(ri).compareTo(BigDecimal.ZERO) < 0) {
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return R.error("价格均不可为负数！");
                    }
                    rsCostprice = rsCostprice.add(kcInprice.get(ri)).subtract(inprice);
                    if (inprice.compareTo(kcInprice.get(ri)) != 0) {
                        String comment = "· id:" + wxkcId + " 成本由" + inprice + "更改为" + kcInprice.get(ri);
                        shLogs.add(new ShouhouLogNewBo(shouhouId, comment, user, null, false));
                    }
                    inprice = kcInprice.get(ri);
                    wxkcoutputUpdateWrapper.set(Wxkcoutput::getInprice, kcInprice.get(ri));
                }
            }
            //
            if(isSetPriceAndPriceGs) {
                wxkcoutputUpdateWrapper.set(Wxkcoutput::getPrice, kcPrice.get(ri)).set(Wxkcoutput::getPriceGs, kcPriceGs.get(ri))
                        .set(Wxkcoutput::getMemberDiscountAmount, 0);
            }

            if (StringUtils.isNotEmpty(wxkcoutputUpdateWrapper.getSqlSet())) {
                wxkcoutputUpdateWrapper.eq(Wxkcoutput::getId, wxkcId);
                wxkcoutputService.update(wxkcoutputUpdateWrapper);
            }
            ri++;
        }

        updateWrapper.set(Shouhou::getFeiyong, rsFeiyong).set(Shouhou::getCostprice, rsCostprice);

        return R.success("操作成功");
    }

    private void handleDiscountResult(LambdaUpdateWrapper<Wxkcoutput> wxkcoutputUpdateWrapper, UseDiscountBo useDiscount, R<DiscountInfoBo> discountR, Consumer<DiscountInfoBo> successCallback) {
        if(discountR.isSuccess()){
            wxkcoutputUpdateWrapper.set(Wxkcoutput::getMemberDiscountAmount, useDiscount.getDiscountAmount());
            if(successCallback != null){
                successCallback.accept(discountR.getData());
            }
            if(useDiscount.getPriceTypeEnum() == UseDiscountBo.PriceTypeEnum.PJ){
                wxkcoutputUpdateWrapper.set(Wxkcoutput::getPrice, useDiscount.getPrice());
            }else{
                wxkcoutputUpdateWrapper.set(Wxkcoutput::getPriceGs, useDiscount.getPrice());
            }
        }
    }

    private Boolean yuyuecomplete(Integer id) {
        LambdaUpdateWrapper<ShouhouYuyue> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ShouhouYuyue::getId,id);
        lambdaUpdateWrapper.set(ShouhouYuyue::getStats, 3);
        lambdaUpdateWrapper.in(ShouhouYuyue::getStats, Arrays.asList(6, 7));
        lambdaUpdateWrapper.and(bo -> bo.eq(ShouhouYuyue::getIszy, 0).or().isNull(ShouhouYuyue::getIszy));
        return shouhouYuyueService.update(lambdaUpdateWrapper);
    }

    private List<String> getFaultOptionList(List<TuihuanConfigRes> tuihunConfig) {
        List<String> faultOptions = tuihunConfig.stream().map(e -> e.getFaultOption()).distinct().collect(Collectors.toList());
        List<String> faultOptionList = new LinkedList<>();
        faultOptions.stream().forEach(e -> {
            String[] optionArray = e.split(",");
            for (String m : optionArray) {
                if (!faultOptionList.contains(m)) {
                    faultOptionList.add(m);
                }
            }
        });
        return faultOptionList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unlockWxpjByYyid(Integer yyid) {
        List<ShouhouYuyuelockppids> list = yuyuelockppidsService.list(new LambdaQueryWrapper<ShouhouYuyuelockppids>().eq(ShouhouYuyuelockppids::getYyid, yyid));
        if (CollectionUtils.isNotEmpty(list)) {
            for (ShouhouYuyuelockppids yuyuelockppids : list) {
                //删除预留库存
                this.lockWxPeijian(yuyuelockppids.getYyid(), yuyuelockppids.getPpid(), yuyuelockppids.getAreaid(), LockWxPeijianTypeEnum.NO_LOCK.getCode());
            }
        }
    }

    @Override
    public Shouhou getNotBaoxiu(String imei, boolean islp,LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.getNotBaoxiu(imei, islp,startTime,endTime);
    }

    @Override
    public Shouhou getShouhouNotDel(Integer id, Integer xTenant, Integer areaId, boolean hasAuthPart, Integer authorizeId) {
        return baseMapper.getShouhouNotDel(id, xTenant, areaId, hasAuthPart, authorizeId);
    }

    @Override
    public List<BindPpidKcInfo> getBindPpidKcInfo(Integer ppid, Integer xtenant) {
        OaUserBO user = currentRequestComponent.getCurrentStaffId();
        return baseMapper.getBindPpidKcInfo(ppid,xtenant, user.getAreaId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<List<ShouhouCostPriceRes>> addBindCostPrice(WxFeeBo wxFeeBo, boolean autoOutPut, Boolean isKcLock,boolean isAnyErrorRollBack) {
        if(wxFeeBo.getPpid() == null){
            return R.error("主配件ppid不能为空!");
        }
        List<BindPpidInfoBo> bpiList = wxFeeBo.getBindPpidInfos();
        if(CollectionUtils.isEmpty(bpiList)){
            return R.error("绑定配件不能为空!");
        }
        Map<Integer,Boolean> distinctMap = Maps.newHashMapWithExpectedSize(bpiList.size());
        R<List<ShouhouCostPriceRes>> result = null;
        String userMsg;
        //内部不允许抛出任何异常,包括客户异常,所有已知问题都通过R.error返回,并带上配件id
        List<R<ShouhouCostPriceRes>> rList = new LinkedList<>();
        try {
            //查询结果按创建时间倒序
            for (BindPpidInfoBo infoBo : bpiList) {
                if(distinctMap.containsKey(infoBo.getPpid())){
                    //重复项,跳过
                    continue;
                }
                checkAndAddCost(wxFeeBo, autoOutPut, isKcLock, distinctMap, rList, infoBo);
            }
            boolean isErrorAndRollBack = isAnyErrorRollBack && rList.stream().anyMatch(r -> r.getCode() != ResultCode.SUCCESS);
            if(isErrorAndRollBack){
                userMsg = rList.stream().filter(r->r.getCode() != ResultCode.SUCCESS)
                        .map(R::getUserMsg).collect(Collectors.joining(SignConstant.ZHENG_XIE_GANG));
                result = R.error(userMsg);
            }else{
                userMsg = rList.stream().map(R::getUserMsg).collect(Collectors.joining(SignConstant.ZHENG_XIE_GANG));
                result = R.success(userMsg,rList.stream().map(R::getData).filter(ObjectUtils::isNotNull).collect(Collectors.toList()));
            }

        } finally {
            boolean isErrorAndRollBack = isAnyErrorRollBack && (result == null || result.getCode() != ResultCode.SUCCESS);
            if(isErrorAndRollBack){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        }
        return result;
    }

    @Override
    public Map<Integer,List<ShouhouLogBo>> listShouhouLogs(List<Integer> shouhouIds, Integer type) {
        if(CollUtil.isEmpty(shouhouIds)){
            return Collections.emptyMap();
        }
        Iterable<ShouhouLogNew> shouhouLogNews = shouhouLogNewRepository.findAllById(shouhouIds);
        Map<Integer,List<ShouhouLogBo>> result = new HashMap<>();
        for (ShouhouLogNew shouhouLogNew : shouhouLogNews) {
            result.put(shouhouLogNew.getId(), Optional.ofNullable(shouhouLogNew.getCons()).map(cons -> cons.stream()
                    //过滤类型
                    .filter(con -> type == null || Objects.equals(con.getType(), type))
                    //转为返回结果对象
                    .map(con -> new ShouhouLogBo().setId(shouhouLogNew.getId()).setComment(con.getComment())
                            .setInUser(con.getInUser()).setIsweb(con.getIsWeb())
                            .setDTime(DateUtil.localDateTimeToMinutesStr(con.getDTime()))
                            .setDTimeSecond(DateUtil.getDateTimeAsString(con.getDTime()))
                            .setType(con.getType()))
                    .collect(Collectors.toList()))
                    .orElseGet(Collections::emptyList));
        }
        return result;
    }

    @Override
    public R<Dict> checkNoReasonReturn(List<Integer> pIds) {
        //为空返回false
        if(CollUtil.isEmpty(pIds)){
            return R.success(Dict.create().set("isNoReason",false));
        }
        //没有限制即允许无理由退货
        boolean isNoReason = isNoReason(pIds);
        return R.success(Dict.create().set("isNoReason",isNoReason));
    }

    @Override
    public boolean isNoReason(List<Integer> pIds) {
        ProductSupportCloud productSupportCloud = SpringUtil.getBean(ProductSupportCloud.class);
        return !Optional.ofNullable(productSupportCloud.productSupportCheck(pIds)).map(R::getData)
                .map(data -> data.stream().allMatch(ProductSupportCloudVo::isActive) && pIds.stream()
                        .allMatch(pid->data.stream().map(ProductSupportCloudVo::getPid).anyMatch(pid::equals)))
                .filter(Boolean::booleanValue).isPresent();
    }

    @Override
    public Integer countShouhouTimes(String imei, Integer userId, LocalDateTime tradeDate) {
        return baseMapper.countShouhouTimes(imei,userId,tradeDate);
    }
    @Override
    public R quji(QujiReq qujiReq) {
        //是否存在审核中的退订信息。如果存在，则toast提示【请先审核或删除退订申请，再进行取机】，且取机失败。如果不存在，则正常取机完成。
        Integer isTui = baseMapper.getShouhouTuiKuan(qujiReq.getId());
        if (Boolean.FALSE.equals(CommenUtil.isNullOrZero(isTui))) {
            return R.error("存在未完结的退订退换记录，请取消后再完成订单");
        }
        // 存在单独收银配件, 如果没有退换记录不允许取机
        List<Integer> returnPayCodes = Arrays.stream(Wxkcoutput.PartTypeEnum.values())
                .filter(Wxkcoutput.PartTypeEnum::isRefundPay).map(Wxkcoutput.PartTypeEnum::getCode).collect(Collectors.toList());
        boolean isReturnPay = wxkcoutputService.lambdaQuery().eq(Wxkcoutput::getWxid, qujiReq.getId())
                .in(Wxkcoutput::getPartType, returnPayCodes).count() > 0;
        LambdaQueryChainWrapper<ShouhouTuihuan> existMachineTuihuanWrapper = shouhouTuihuanService.lambdaQuery()
                .eq(ShouhouTuihuan::getShouhouId, qujiReq.getId()).and(CommenUtil.isNullOrEq(ShouhouTuihuan::getIsdel, 0))
                .in(ShouhouTuihuan::getTuihuanKind, TuihuanKindEnum.listMachineRefundKind().stream()
                        .map(TuihuanKindEnum::getCode).collect(Collectors.toList()));
        if(isReturnPay && existMachineTuihuanWrapper.count() <= 0){
            return R.error("请先提交退款申请");
        }
        //取机前重新计算维修费用,避免各种原因导致维修费用和配件费用不一致
        Long xtenant = Convert.toLong(XtenantEnum.getXtenant());
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        CompletableFuture.runAsync(() -> currentRequestComponent.invokeWithUser(xtenant, oaUser,
                ou -> {
                    shouHouPjService.updateShouhouFeiyong(qujiReq.getId());
                    return null;
                }))
                .exceptionally(e -> {
                    throw new CustomizeException(e.getMessage(),e);
                }).join();
        //已收是否等于总计维修款。如果等于，则可以取机；如果不等于，则toast提示【已收等于维修款才能取机哦】，且取机失败。
        Shouhou shouhou = baseMapper.getByIdSqlServer(qujiReq.getId());
        if (ObjectUtil.isNull(shouhou) || CommenUtil.isNullOrZero(shouhou.getId())){
            return R.error("售后单id错误！");
        }
        if(Boolean.TRUE.equals(shouhou.getIsquji())){
            return R.error("售后单已取机, 请勿重复取机！");
        }
        if (WxStatusEnum.YXH.getCode().equals(shouhou.getStats()) && StrUtil.isBlank(shouhou.getWeixiuren())){
            return R.error("取机异常, 已修好状态下，维修人不能为空！");
        }
        if (Optional.ofNullable(shouhou.getYifum()).orElse(BigDecimal.ZERO).compareTo(Optional.ofNullable(shouhou.getFeiyong()).orElse(BigDecimal.ZERO)) != 0){
            return R.error("已收等于维修款才能取机哦");
        }
        return Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).filter(R::isSuccess)
                .map(R::getData).filter(StrUtil::isNotBlank)
                .map(oaUrl -> invokeHttpQuji(qujiReq, oaUrl))
                .orElseGet(() -> new R<>(ResultCode.SERVER_ERROR, "获取取机的地址异常"));
    }

    private R<?> invokeHttpQuji(QujiReq qujiReq, String oaUrl) {
        String url = StrUtil.format("{}/oa/shouhou/quji/{}", oaUrl, qujiReq.getId());
        HttpRequest post = HttpUtil.createPost(url);
        SpringContextUtil.getRequest().map(request -> post.auth(request.getHeader("Authorization")));
        HttpResponse qujiResp = post.body(JSON.toJSONString(qujiReq), ContentType.JSON.getValue())
                .cookie(SpringContextUtil.getRequest().map(request -> request.getCookies())
                        .map(cookies -> Arrays.stream(cookies).map(cookie -> StrUtil.format("{}={}",cookie.getName(), cookie.getValue())).collect(Collectors.joining(";")))
                        .orElseThrow(() -> new CustomizeException("只能通过http请求进行取机")))
                .execute();
        //三方订单状态同步 无论是否成功都同步
        thirdPlatStatusSyncService.thirdShouhouStatusSync(ShouhouOrderTypeEnum.SHOUHOU.getCode(), qujiReq.getId());
        //获取售后信息
        Shouhou shouhou = baseMapper.getByIdSqlServer(qujiReq.getId());
        if(Boolean.TRUE.equals(shouhou.getIsquji())){
            try {
                shouhouService.saveShouhouServiceRecord(qujiReq, shouhou);
            } catch (Exception e) {
                RRExceptionHandler.logError(StrUtil.format("维修单[{}]取机成功保存服务记录", qujiReq.getId()), qujiReq, e, smsService::sendOaMsgTo9JiMan);
            }
            try {
                //兼容处理,shouyinglock为1,跟新成功与否不影响流程
                shouhouService.lambdaUpdate().set(Shouhou::getShouyinglock, 1).eq(Shouhou::getId, qujiReq.getId())
                        .and(cnd -> cnd.eq(Shouhou::getShouyinglock, 0).or().isNull(Shouhou::getShouyinglock)).gt(Shouhou::getYifum, 0)
                        .update();
            } catch (Exception e) {
                RRExceptionHandler.logError(StrUtil.format("维修单[{}]取机成功更新收银锁定", qujiReq.getId()), qujiReq, e, smsService::sendOaMsgTo9JiMan);
            }
            try {
                //取机后,优惠码分摊
                shouhouService.qujiYouhuiMaAllocation(qujiReq.getId(), shouhou);
            } catch (Exception e) {
                RRExceptionHandler.logError(StrUtil.format("维修单[{}]取机成功优惠码分摊", qujiReq.getId()), qujiReq, e, smsService:: sendOaMsgTo9JiMan);
            }
            try {
                //取机后,售后出险补偿
                shouhouService.qujiUseServiceRecord(qujiReq.getId(), shouhou);
            } catch (Exception e) {
                RRExceptionHandler.logError(StrUtil.format("维修单[{}]取机成功补偿出险", qujiReq.getId()), qujiReq, e, smsService:: sendOaMsgTo9JiMan);
            }
            try {
                //维修配件发券
                shouhouService.sendCouponsParts(shouhou);
            }catch (Exception e) {
                RRExceptionHandler.logError(StrUtil.format("维修单[{}]售后服务出售赠送回收加价券功能异常", qujiReq.getId()), qujiReq, e, smsService:: sendOaMsgTo9JiMan);
            }
            //广播维修单的取机消息
            SpringUtil.getBean("oaRabbitTemplate",RabbitTemplate.class).convertAndSend(RabbitMqConfig.AFTER_SHOUHOU_QU_JI,"",Convert.toStr(qujiReq.getId()));
        }

        if(qujiResp.isOk()){
            //成功
            JSONObject result = JSON.parseObject(qujiResp.body());
            if (Objects.equals(Integer.valueOf(1), result.getInteger("stats"))) {

                CompletableFuture.runAsync(() -> {
                    //跳转连接
                    String host = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL))
                            .filter(r -> ResultCode.SUCCESS == r.getCode())
                            .map(R::getData)
                            .orElseThrow(() -> new RRException("获取域名出错"));
                    String inuser = shouhou.getInuser();
                    if (StringUtils.isNotEmpty(inuser)){
                        List<TransferUserRes> transferUserRes = smallproWxMapper.selectUserNameList(Collections.singletonList(inuser));
                        if (CollectionUtils.isNotEmpty(transferUserRes)){
                            Integer areaId = transferUserRes.get(0).getAreaId();
                            Areainfo areainfo = areainfoService.getById(areaId);
                            String message = "你关注的员工【" + inuser + "（" + areainfo.getArea() + "）】添加的维修单（单号：" + shouhou.getId() + "）处理完成，请做好跟进~";
                            String link = host + "/new/#/afterService/order/detail/" + shouhou.getId();
                            this.sendSubCollectionMessage(transferUserRes.get(0).getCh999UserId(), link, message);
                        }
                    }
                    String weixiuren = shouhou.getWeixiuren();
                    if (StringUtils.isNotEmpty(weixiuren)){
                        List<TransferUserRes> transferUserRes = smallproWxMapper.selectUserNameList(Collections.singletonList(weixiuren));
                        if (CollectionUtils.isNotEmpty(transferUserRes)){
                            Integer areaId = transferUserRes.get(0).getAreaId();
                            Areainfo areainfo = areainfoService.getById(areaId);
                            String message = "你关注的员工【" + weixiuren + "（" + areainfo.getArea() + "）】添加的维修单（单号：" + shouhou.getId() + "）处理完成，请做好跟进~";
                            String link = host + "/new/#/afterService/order/detail/" + shouhou.getId();
                            this.sendSubCollectionMessage(transferUserRes.get(0).getCh999UserId(), link, message);
                        }
                    }
                });
                return R.success(result);
            }
            return new R<>(ResultCodeEnum.SERVER_ERROR.getCode(),result.getString("exMsg"),result.getString("msg"));
        }

        String errorMsg = StrUtil.format("维修单[{}]调用取机接口发生{}异常", qujiReq.getId(), qujiResp.getStatus());
        if(!Boolean.TRUE.equals(shouhou.getIsquji())){
            //取机失败才进行通知, 就怕时差问题
            RRExceptionHandler.logError(errorMsg, qujiReq, null, smsService::sendOaMsgTo9JiMan);
        }
        return R.error(errorMsg).addBusinessLog(url)
                .addBusinessLog(qujiResp.body());
    }

    /**
     * 判断售后单是否可以修改串号
     * @param shouhou
     * @return
     */
    private Boolean whetherFixImei(Shouhou shouhou){
        //维修单状态为：已修好、处理中，修不好
        List<Integer> wxStatusList = Arrays.asList(WxStatusEnum.YXH.getCode(), WxStatusEnum.CLZ.getCode(), WxStatusEnum.XBH.getCode());
        boolean statsFlag = wxStatusList.contains(shouhou.getStats());
        //维修单未取机
        boolean isNotQuJiFlag = Boolean.FALSE.equals(shouhou.getIsquji());
        //维修单为客户维修单（排除现货维修单）
        boolean customerFlag = !SPOT_GOODS_USER_ID.equals(shouhou.getUserid());
        //维修单没有退换记录（只查询未删除的，删除的剔除）
        boolean noRecordFlag = CommenUtil.isNullOrZero(baseMapper.canChangeImei(shouhou.getId()));
        //1,2,3
        boolean isDji = SpringUtil.getBean(ShouhouExtendService.class).isDJIRepairOrder(null, shouhou.getId());
        return statsFlag && isNotQuJiFlag && customerFlag && noRecordFlag && !(isDji && ObjectUtil.defaultIfNull(shouhou.getYifum(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0);
    }
    @Override
    public R<UpdateShouHouImeiRes> updateShouHouImei(UpdateShouHouImeiReq req) {
        Shouhou shouhou = Optional.ofNullable(this.getById(req.getShouHouId())).orElseThrow(() -> new CustomizeException("售后单查询为空"));
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前用户为空"));
        if(!whetherFixImei(shouhou)){
           return R.error("维修单不符合修改条件");
        }
        //获取老串号
        String imeiOld = Optional.ofNullable(shouhou.getImei()).orElse("");
        //获取新串号
        String imeiNew = Optional.ofNullable(req.getImei()).orElse("");
        if(imeiOld.equals(imeiNew)){
            return R.error("串号相同无须修改");
        }
        UpdateShouHouImeiRes updateShouHouImeiRes = new UpdateShouHouImeiRes();
        List<ShouHouImeiInfo> shouHouImeiInfoList = new ArrayList<>();
        //老串号相关数据
        ServiceInfoVO recordOld = Optional.ofNullable(serviceRecordService.getRecord(imeiOld, null, Boolean.TRUE)).orElse(new ServiceInfoVO());
        Optional.ofNullable(createShouHouImeiInfo(recordOld, UpdateImeiRankEnum.UPDATE_BEFORE_RANK.getCode(),shouhou,req)).ifPresent(shouHouImeiInfoList::add);
        //新串号相关数据
        ServiceInfoVO recordNew = Optional.ofNullable(serviceRecordService.getRecord(imeiNew, null, Boolean.TRUE)).orElse(new ServiceInfoVO());
        ShouHouImeiInfo shouHouImeiInfoNew = createShouHouImeiInfo(recordNew, UpdateImeiRankEnum.UPDATE_AFTER_RANK.getCode(),shouhou,req);
        Optional.ofNullable(shouHouImeiInfoNew).ifPresent(shouHouImeiInfoList::add);
        //设置返回数据 (并且数组根据rank排序)
        updateShouHouImeiRes.setShouHouImeiInfoList(shouHouImeiInfoList.stream().sorted(Comparator.comparing(ShouHouImeiInfo::getRank)).collect(Collectors.toList()));
        LambdaUpdateChainWrapper<Shouhou> wrapper = shouhouService.lambdaUpdate().set(Shouhou::getImei, req.getImei()).eq(Shouhou::getId, req.getShouHouId());
        //修改成功 进行日志记录
        String comment;
        //判断新的串号是否存在订单信息
        if(ObjectUtil.isNotNull(shouHouImeiInfoNew) && CommenUtil.isNotNullZero(shouHouImeiInfoNew.getSubId())){
            Integer subId = Optional.ofNullable(shouHouImeiInfoNew.getSubId()).orElse(NumberConstant.ZERO);
            Integer subType = Optional.ofNullable(shouHouImeiInfoNew.getSubType()).orElse(Integer.MAX_VALUE);
            comment = String.format("串号由%s修改为%s,关联订单号%s 同步修改信息", shouhou.getImei(), req.getImei(),getSubIdLink(subId,subType));
            Integer isHuiShou = SimpleServiceSubInfoBo.OrderTypeEnum.LP_ORDER.getCode().equals(recordNew.getOrderType()) ? NumberConstant.ONE : NumberConstant.ZERO;
            //最后一次是回收标识 如果是为true的情况那就不进行以下相关信息的修改
            Boolean lastRecoverFlag = Optional.ofNullable(recordNew.getLastRecoverFlag()).orElse(Boolean.FALSE);
            if(!lastRecoverFlag){
                wrapper.set(Shouhou::getBaoxiu,recordNew.getBaoXiu())
                        .set(Shouhou::getTradedate,recordNew.getTradeDate())
                        .set(Shouhou::getPpriceid,recordNew.getPpriceId())
                        .set(Shouhou::getProductId,recordNew.getProductId())
                        .set(Shouhou::getProductColor,recordNew.getProductColor())
                        .set(Shouhou::getName,recordNew.getProductName())
                        .set(Shouhou::getBuyareaid,recordNew.getMkcAreaId())
                        .set(Shouhou::getSubId,recordNew.getSubId())
                        .set(Shouhou::getBasketId,recordNew.getBasketId())
                        .set(Shouhou::getIshuishou,isHuiShou);
            }
        } else {
            wrapper.set(CommenUtil.isNotNullZero(shouhou.getBuyareaid()),Shouhou::getBuyareaid,null)
                    .set(Objects.nonNull(shouhou.getTradedate()),Shouhou::getTradedate,null)
                    .set(CommenUtil.isNotNullZero(shouhou.getSubId()),Shouhou::getSubId,null)
                    .set(CommenUtil.isNotNullZero(shouhou.getBasketId()),Shouhou::getBasketId,null)
                    .set(Shouhou::getBaoxiu,BaoxiuStatusEnum.WX.getCode())
                    .set(CommenUtil.isNotNullZero(shouhou.getIshuishou()),Shouhou::getIshuishou,0);
            comment = String.format("串号由%s修改为%s,未查询到购买订单信息", shouhou.getImei(), req.getImei());
        }
        MultipleTransaction.build()
                .execute(DataSourceConstants.DEFAULT, () ->{
                    //数据修改
                    boolean update = wrapper.update();
                    log.warn("售后单变更串号,shouhou:{},recordNew:{}, update:{}", JSONUtil.toJsonStr(shouhou), recordNew, update);
                    if(!update){
                        throw new CustomizeException("数据修改失败");
                    }
                    ShouhouLogNoticeBo shouhouLogNoticeBo = new ShouhouLogNoticeBo();
                    shouhouLogNoticeBo.setNeedNotice(false);
                    shouhouLogsService.addShouhouLog(userBO.getUserName(), shouhou.getId(), ShouHouLogTypeEnum.CLXX.getCode(),
                            comment, shouhouLogNoticeBo, false, 0);
                })
                .commit();
        return R.success(updateShouHouImeiRes);
    }

    /**
     * 获取订单连接
     * @param subId
     * @param subType
     * @return
     */
    private String getSubIdLink(Integer subId,Integer subType){
        subId = Optional.ofNullable(subId).orElse(NumberConstant.ZERO);
        subType = Optional.ofNullable(subType).orElse(Integer.MAX_VALUE);
        String link ;
        if(SimpleServiceSubInfoBo.OrderTypeEnum.NEW_ORDER.getCode().equals(subType)){
            link = "【<a href='/addOrder/editOrder?SubID=" + subId + "' title='点击查看订单详细' >" + subId + "</a>】";
        } else if(SimpleServiceSubInfoBo.OrderTypeEnum.LP_ORDER.getCode().equals(subType)){
            link =  "【<a href='/StockOut/editOrder?SubID=" + subId + "' title='点击查看订单详细' >" + subId + "</a>】";
        } else {
            link = subId.toString();
        }
        return link;
    }


    /**
     * 获取串号相关信息
     * @param record
     * @param rank
     * @return
     */
    private ShouHouImeiInfo createShouHouImeiInfo(ServiceInfoVO record, Integer rank,Shouhou shouhou,UpdateShouHouImeiReq req) {
        ShouHouImeiInfo imeiInfo = new ShouHouImeiInfo();
        Integer ppid = Optional.ofNullable(record.getPpriceId()).orElse(shouhou.getPpriceid());
        String productName = Optional.ofNullable(record.getProductName()).orElse(shouhou.getName());
        String productColor = Optional.ofNullable(record.getProductColor()).orElse(shouhou.getProductColor());
        if(UpdateImeiRankEnum.UPDATE_BEFORE_RANK.getCode().equals(rank)){
            imeiInfo.setImei(shouhou.getImei());
        } else{
            imeiInfo.setImei(req.getImei());
        }
        if(ObjectUtil.isNotNull(record.getOrderType())){
            String productTypeMsg = "";
            if (SimpleServiceSubInfoBo.OrderTypeEnum.NEW_ORDER.getCode().equals(record.getOrderType())
                    || SimpleServiceSubInfoBo.OrderTypeEnum.HISTORY_RECORD_ORDER.getCode().equals(record.getOrderType())) {
                productTypeMsg = "新机";
                if(BasketTypeEnum.BASKET_TYPE_DEFECT_MACHINE.getCode().equals(record.getBasketType())) {
                    productTypeMsg = "优品";
                }
            } else if (SimpleServiceSubInfoBo.OrderTypeEnum.LP_ORDER.getCode().equals(record.getOrderType())) {
                productTypeMsg = "良品";
            } else if (SimpleServiceSubInfoBo.OrderTypeEnum.WAI_XIU_ORDER.getCode().equals(record.getOrderType())) {
                productTypeMsg = "外修";
            }
            imeiInfo.setProductTypeMsg(productTypeMsg)
                    .setIshuishou(SimpleServiceSubInfoBo.OrderTypeEnum.LP_ORDER.getCode().equals(record.getOrderType())?NumberConstant.ONE:NumberConstant.ZERO);
        }
        if(ObjectUtil.isNotNull(record.getBaoXiu())){
            imeiInfo.setBaoXiu(Optional.ofNullable(record.getBaoXiu()).orElse(Boolean.FALSE)?NumberConstant.ONE:NumberConstant.ZERO);
        }
        imeiInfo.setModel("[" + ppid + "]" + Optional.ofNullable(productName).orElse("") + Optional.ofNullable(productColor).orElse(""));
        //最后一次是回收标识 如果是为true的情况那就不进行以下相关信息的修改
        Boolean lastRecoverFlag = Optional.ofNullable(record.getLastRecoverFlag()).orElse(Boolean.FALSE);
        if(!lastRecoverFlag){
            imeiInfo.setSubId(record.getSubId())
                    .setSubType(record.getOrderType())
                    .setUserId(record.getUserId())
                    .setUserClass(record.getUserClass())
                    .setCh999User(record.getUsername())
                    .setUserName(record.getUsername())
                    .setUserMobile(record.getSubMobile())
                    .setUserClassName(record.getUserClassName())
                    .setServiceVos(record.getServiceVos());
        }

        imeiInfo.setRank(Optional.ofNullable(rank).orElse(Integer.MAX_VALUE));
        return imeiInfo;
    }

    public void qujiUseServiceRecord(Integer shouhouId){
        Shouhou shouhou = shouhouService.getById(shouhouId);
        shouhouService.qujiUseServiceRecord(shouhouId, shouhou);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void qujiYouhuiMaAllocation(Integer wxId, Shouhou shouhou) {
        ShouHouPjMapper shouHouPjMapper = SpringUtil.getBean(ShouHouPjMapper.class);
        //使用过优惠码，则返回优惠码
        List<Integer> cardLogIds = shouHouPjMapper.getCardIdfromCardLogsByWxId(wxId, null, null);
        BigDecimal youhuiMaTotal = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(cardLogIds)){
            List<NumberCard> drList = numberCardService.lambdaQuery().in(NumberCard::getId, cardLogIds).list();
            for (NumberCard dr : drList) {
                //获取能够使用优惠码的配件
                Set<Integer> wxkcIds = Optional.ofNullable(shouhouExService.listYouhuimaPj(dr, shouhou)).map(R::getData).orElseGet(Collections::emptySet);
                //到最后为空就是没有限制配件,可以全部进行分摊
                List<Wxkcoutput> wxkcoutputs = Optional.ofNullable(wxkcoutputService.listYouhuiMaAllocation(shouhou.getId(),
                        wxkcIds,shouhouConstants.getShouhouServicesPpriceids())).filter(CollUtil::isNotEmpty)
                        //找不到分摊的配件,应该都是售后服务,只能分摊到服务上,不然会报错
                        .orElseGet(()->wxkcoutputService.listYouhuiMaAllocation(shouhou.getId(), wxkcIds,null));
                //分摊计算
                shouhouExService.youhuiMaAllocation(dr.getTotal(), wxkcoutputs);
                //批量更新分摊费用,并设置当前分摊优惠码的code,没有参与分摊的,且优惠费用为null设置为零
                wxkcoutputService.updateYouHuiFeiYongBatch(shouhou.getId(), dr.getCardID(), wxkcoutputs);
                youhuiMaTotal = youhuiMaTotal.add(dr.getTotal());
                //记录分摊日志
                SpringContextUtil.getRequest()
                        .ifPresent(req -> {
                            List<Runnable> logLambdas = (List<Runnable>) req.getAttribute(RequestAttrKeys.REQUEST_ATTR_SHOUHOU_LOG_LAMBDA);
                            if(logLambdas == null){
                                logLambdas = new LinkedList<>();
                                req.setAttribute(RequestAttrKeys.REQUEST_ATTR_SHOUHOU_LOG_LAMBDA,logLambdas);
                            }
                            logLambdas.add(()->shouhouService.saveShouhouLog(shouhou.getId(), StrUtil.format("优惠码[{}]分摊优惠费用到配件id:[{}]", dr.getCardID()
                                    , wxkcoutputs.stream().map(Wxkcoutput::getId).map(String::valueOf).collect(Collectors.joining(SignConstant.COMMA)))
                                    , "系统", null, false));
                        });
            }
        }
        //剩余费用的分摊
        BigDecimal totalYouhuiFeiyong = ObjectUtil.defaultIfNull(shouhou.getYouhuifeiyong(), BigDecimal.ZERO);
        if(totalYouhuiFeiyong.subtract(youhuiMaTotal).compareTo(BigDecimal.ZERO)>0){
            //需要重新计算,避免重复分摊
            //全部配件进行分摊
            List<Wxkcoutput> wxkcoutputs = wxkcoutputService.listYouhuiMaAllocation(shouhou.getId(), null,null);
            totalYouhuiFeiyong = totalYouhuiFeiyong.subtract(wxkcoutputs.stream().map(Wxkcoutput::getYouhuifeiyong)
                    .filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            //分摊计算
            shouhouExService.youhuiMaAllocation(totalYouhuiFeiyong, wxkcoutputs);
            //批量更新分摊费用,并设置当前分摊优惠码的code,没有参与分摊的,且优惠费用为null设置为零
            wxkcoutputService.updateYouHuiFeiYongBatch(shouhou.getId(), null, wxkcoutputs);
        }
        SpringContextUtil.getRequest()
                .map(req -> (List<Runnable>) req.getAttribute(RequestAttrKeys.REQUEST_ATTR_SHOUHOU_LOG_LAMBDA))
                .ifPresent(logLambdas -> logLambdas.forEach(Runnable::run));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveShouhouServiceRecord(QujiReq qujiReq, Shouhou shouhou) {
        //取机成功后的操作,插入服务记录
        List<Wxkcoutput> wxkcoutputs = wxkcoutputService.list(new LambdaQueryWrapper<Wxkcoutput>()
                .eq(Wxkcoutput::getWxid, qujiReq.getId()).ne(Wxkcoutput::getStats, 3));
        List<Integer> wxkcPpids = wxkcoutputs.stream().map(Wxkcoutput::getPpriceid)
                .filter(Objects::nonNull).filter(ppid -> ppid > 0).collect(Collectors.toList());
        if(wxkcPpids.isEmpty()){
            return;
        }
        //批量获取分类信息
        List<Integer> wxkCids = productinfoService.getCidsByPpid(wxkcPpids);
        List<Category> categorys = SpringUtil.getBean(CategoryService.class).listAll();

        //维修单出配件时配件购买服务记录表
        WebCloud webCloud = SpringUtil.getBean(WebCloud.class);
        RetryService retryService = SpringUtil.getBean(RetryService.class);
        Optional.ofNullable(retryService.retryByFeignRetryableException(()->
                webCloud.listProductServicesV2(Convert.toInt(shouhou.getProductId(),0), wxkcPpids, XtenantEnum.getXtenant())))
                .filter(r -> r.getCode() == Result.SUCCESS).map(Result::getData)
                .map(Map::entrySet)
                .ifPresent(entrys -> {
                    entrys.stream().filter(e -> CollUtil.isNotEmpty(e.getValue()))
                            .forEach(entry -> {
                                entry.getValue().stream().map(ProRelateInfoService::getServiceType)
                                        .distinct()
                                        .forEach(serviceType -> {
                                            Wxkcoutput wxkcoutput = wxkcoutputs.stream()
                                                    .filter(wkc -> Objects.equals(wkc.getPpriceid(), entry.getKey())).findFirst()
                                                    .orElse(null);
                                            if (Objects.isNull(wxkcoutput)
                                                    || wxProductServiceOpeningTypeService.lambdaQuery()
                                                    .eq(WxProductServiceOpeningType::getProductServiceOpeningType, serviceType)
                                                    .eq(WxProductServiceOpeningType::getWxkcid, wxkcoutput.getId()).count()>0) {
                                                return;
                                            }
                                            WxProductServiceOpeningType wx = new WxProductServiceOpeningType();
                                            wx.setWxkcid(wxkcoutput.getId());
                                            wx.setProductServiceOpeningType(serviceType);
                                            wxProductServiceOpeningTypeService.save(wx);
                                        });

                            });

                });

        Optional.ofNullable(retryService.retryByFeignRetryableException(()->
                    webCloud.openingGetServices(CollUtil.join(wxkcPpids, ","), XtenantEnum.getXtenant())))
                //只获取成功的结果
                .filter(r -> {
                    boolean isSuccess = r.getCode() == Result.SUCCESS;
                    if(!isSuccess){
                        throw new CustomizeException(StrUtil.format("获取服务配置, 网站接口返回: {}", JSON.toJSONString(r)));
                    }
                    return isSuccess;
                })
                .map(Result::getData)
                .ifPresent(psoList -> {
                    psoList.stream().collect(Collectors.groupingBy(ProductServiceOpeningBO::getPpid)).entrySet()
                            .stream()
                            .map(entry -> entry.getValue().stream()
                                    //分类级别倒序排
                                    .sorted(Comparator.comparing(pso -> categorys.stream().filter(c -> Objects.equals(c.getId(), pso.getCategoryId()))
                                            .findFirst().map(Category::getLevel).orElse(-1), Comparator.reverseOrder()))
                                    .filter(pso -> wxkCids.contains(pso.getCategoryId())).findFirst().orElse(null))
                            .filter(Objects::nonNull)
                            .forEach(pso -> {
                                List<Wxkcoutput> serviceWxkcoutputs = wxkcoutputs.stream()
                                        .filter(wkc -> Objects.equals(wkc.getPpriceid(), pso.getPpid()))
                                        .collect(Collectors.toList());
                                if(serviceWxkcoutputs.isEmpty()){
                                    throw new CustomizeException(StrUtil.format("找不到{}服务对应的配件记录", pso.getPpid()));
                                }
                                for (Wxkcoutput wxkcoutput : serviceWxkcoutputs) {
                                    Integer wxkcId = wxkcoutput.getId();
                                    double price = ObjectUtil.defaultIfNull(wxkcoutput.getPrice(), BigDecimal.ZERO).doubleValue();
                                    ServiceRecord serviceRecord = buildServiceRecord(shouhou, pso, wxkcId, price);
                                    //查询数据库中的服务
                                    boolean serviceNotExists = serviceRecordService.count(new LambdaQueryWrapper<ServiceRecord>()
                                            .eq(ServiceRecord::getSubId, shouhou.getId()).eq(ServiceRecord::getBasketId, wxkcId)
                                            .eq(ServiceRecord::getServiceType, pso.getType())) <= 0;
                                    if (serviceNotExists) {
                                        serviceRecordService.save(serviceRecord);
                                        if (CollUtil.isNotEmpty(pso.getChildren())) {
                                            //保存绑定的服务
                                            pso.getChildren().stream().map(child -> buildServiceRecord(shouhou, child, wxkcId, price).setServicesTypeBindId(serviceRecord.getId()))
                                                    .forEach(serviceRecordService::save);
                                        }
                                    }
                                }
                            });
                });
    }

    @Override
    public Integer getFromsourceById(Integer id) {
        return baseMapper.getFromsourceById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmitCheck()
    public PjtShouhouRes addPjtShouhou(PjtShouhouReq req) {
        PjtShouhouRes result = new PjtShouhouRes();
        Integer mkcId = req.getMkcId();
        RecoverMkc recoverMkc = recoverMkcService.lambdaQuery().eq(RecoverMkc::getId, mkcId).list()
                .stream().findFirst().orElse(null);
        if (Objects.isNull(recoverMkc)) {
            throw new CustomizeException("获取不到库存:" + mkcId);
        }

        Long recoverSubId = req.getRecoverSubId();
        RecoverMarketinfo recoverMarketinfo = recoverMarketinfoService.lambdaQuery().eq(RecoverMarketinfo::getSubId, recoverSubId).list()
                .stream().findFirst().orElse(null);
        if (Objects.isNull(recoverMarketinfo)) {
            throw new CustomizeException("获取不到转售单:" + recoverSubId);
        }

        Integer shouhouId = null;

        Shouhou shouhouExist = this.lambdaQuery().eq(Shouhou::getSubId, recoverSubId)
                .eq(Shouhou::getIshuishou,IshuishouEnum.GOOD_PRODUCT.getCode())
                .list().stream().findFirst().orElse(null);
        if (Objects.nonNull(shouhouExist)){
            shouhouId = shouhouExist.getId();
            Integer fromBasketId = recoverMkc.getFromBasketId();
            RecoverMkc newRecoverMkc = recoverMkcService.lambdaQuery().eq(RecoverMkc::getFromBasketId, fromBasketId)
                    .ne(RecoverMkc::getMkcCheck, 5)
                    .orderByDesc(RecoverMkc::getId).list().stream()
                    .findFirst().orElse(null);
            if (Objects.isNull(newRecoverMkc)){
                throw new CustomizeException("获取不到新机器:" + shouhouId);
            }
            result.setShouhouId(shouhouId);
            result.setNewMkcId(newRecoverMkc.getId());
            return result;
        }
        recoverMarketinfoService.lambdaUpdate().eq(RecoverMarketinfo::getSubId, recoverSubId)
                .set(RecoverMarketinfo::getSubCheck, ESubCheckEnum.NINE.getCode())
                .set(RecoverMarketinfo::getReturnDate, LocalDateTime.now())
                .update();
        BigDecimal returnPrice = req.getReturnPrice();
        Integer recoverBasketId = req.getRecoverBasketId();

        if (Objects.nonNull(returnPrice) && !BigDecimal.ZERO.equals(returnPrice)) {
            recoverMarketsubinfoService.updatePrice(recoverBasketId, returnPrice);
        }

        Integer ppriceid = recoverMkc.getPpriceid();
        if (Objects.isNull(ppriceid) || (0 == ppriceid)) {
            throw new CustomizeException("ppriceid为空:" + recoverMkc.getId());
        }
        Productinfo productinfo = productinfoService.getProductinfoByPpid(ppriceid);
        //写售后退还
        Shouhou shouhou = new Shouhou();

        Integer recoverSubIdInt = Convert.toInt(recoverSubId);
        shouhou.setName(productinfo.getProductName()).setPeizhi("无").setProblem(req.getProblem())
                .setMobile("12345678910").setStats(1).setBaoxiu(1).setInuser("系统").setImei(recoverMkc.getImei())
                .setXianshi(Boolean.TRUE).setTradedate(recoverMarketinfo.getTradedate()).setModidate(LocalDateTime.now()).setFeiyong(BigDecimal.ZERO)
                .setCostprice(BigDecimal.ZERO).setOfftime(LocalDateTime.now()).setArea(recoverMarketinfo.getArea())
                .setAreaid(recoverMarketinfo.getAreaid()).setShouyinglock(0).setMkcId(mkcId)
                .setUserid(0L).setKinds("bd").setIsticheng(Boolean.FALSE).setIssoft(Boolean.FALSE).setModidate(LocalDateTime.now())
                .setProductId(Long.valueOf(productinfo.getProductId())).setProductColor(productinfo.getProductColor()).setBuyarea(recoverMarketinfo.getArea())
                .setPandian(Boolean.FALSE).setPandiandate(LocalDateTime.now()).setPpriceid(ppriceid)
                .setIsquick(Boolean.FALSE).setWcount(0).setIsweixiu(Boolean.TRUE).setWeixiudtime(LocalDateTime.now())
                .setWeixiuStartdtime(LocalDateTime.now()).setOrderid(String.valueOf(recoverMkc.getOrderid())).setIsquji(Boolean.TRUE)
                .setIsfan(Boolean.FALSE).setSubId(recoverSubIdInt).setBasketId(recoverBasketId).setIshuishou(1).setHuiprint(1)
                .setBuyareaid(recoverMarketinfo.getAreaid()).setOrderSource(0);
        this.save(shouhou);
        shouhouId = shouhou.getId();


        //写售后退还
        ShouhouTuihuan tuihuan = new ShouhouTuihuan();
        tuihuan.setShouhouId(shouhouId).setTuihuanKind(TuihuanKindEnum.TK.getCode()).setTuikuanM(recoverMarketinfo.getYifum()).setTuikuanM1(recoverMarketinfo.getYifum())
                .setFaultType("有故障").setCheckType("正常退换").setComment("拍机堂一键退换").setDtime(LocalDateTime.now()).setCheck3(true).setCheck3dtime(LocalDateTime.now()).setCheck3user("系统")
                .setArea(recoverMarketinfo.getArea()).setIsdel(Boolean.FALSE).setZhejiaM(BigDecimal.ZERO).setBuypriceM(recoverMarketinfo.getYifum()).setInprice(recoverMarketinfo.getYifum())
                .setAreaid(recoverMarketinfo.getAreaid()).setCheck1(true).setCheck1dtime(LocalDateTime.now()).setCheck1user("系统").setCheck2(true).setCheck2dtime(LocalDateTime.now()).setCheck2user("系统")
                .setTuiKinds(ShouhouTuiHuanPo.TuiKindsEnum.GROUP_TUI.getCode());
        //改为组合退, 获取三方的收银记录进行关联
        ThirdOriginWayService thirdOriginWayService = SpringUtil.getBean(ThirdOriginWayService.class);
        List<ThirdOriginRefundVo> allThirdOriginRefundVos = thirdOriginWayService.listAll(recoverSubIdInt, TuihuanKindEnum.TK_LP);
        if(allThirdOriginRefundVos.isEmpty()){
            tuihuan.setTuiKinds(ShouhouTuiHuanPo.TuiKindsEnum.NORMAL_TUI.getCode()).setTuiWay("拍机堂返回");
        }
        shouhouTuihuanService.save(tuihuan);
        if(!allThirdOriginRefundVos.isEmpty()){
            thirdOriginWayService.doSave(tuihuan.getId(), "系统", allThirdOriginRefundVos);
        }

        RecoverMkc newRecoverMkc = new RecoverMkc();
        BeanUtils.copyProperties(recoverMkc, newRecoverMkc);
        newRecoverMkc.setId(null);
        newRecoverMkc.setMkcCheck(11);
        newRecoverMkc.setIshouhou(Boolean.TRUE);
        newRecoverMkc.setToBasketId(null);
        newRecoverMkc.setOrderid("");
        newRecoverMkc.setPpriceid(productinfo.getPpriceid());
        recoverMkcService.save(newRecoverMkc);
        Integer newMkcId = newRecoverMkc.getId();

        //写维修日志
        List<String> wxjdCommentList = new ArrayList<>();
        wxjdCommentList.add("*本次售后服务已完成。");
        wxjdCommentList.add("退款办理成功");
        wxjdCommentList.add("您好，已为您的设备办理退款。");
        wxjdCommentList.add(req.getProblem());

        ShouhouLogNoticeBo notice = new ShouhouLogNoticeBo();
        notice.setNeedNotice(false);
        for (String comment : wxjdCommentList) {
            shouhouLogsService.addShouhouLog("系统", shouhouId, ShouHouLogTypeEnum.WXJD.getCode(),
                    comment, notice, false, null);
        }

//
//        shouhouLogsService.addShouhouLog("系统", shouhouId, ShouHouLogTypeEnum.CLXX.getCode(),
//                "·[退款]操作, 售后转自：" + shouhouId, notice, false, null);
        shouhouLogsService.addShouhouLog("系统", shouhouId, ShouHouLogTypeEnum.WXFA.getCode(),
                req.getProblem(), notice, false, null);

        String mkcLog = "售后退货：维修单号：<a href=\"/shouhou/edit/" + shouhouId + "\">" + shouhouId + "</a>，mkc_id:" + mkcId;
        saveRecoverMkcNewLogs(newMkcId, mkcLog, "系统", false);

        // 添加回收库存日志
        PjtRecoverMkcLog recoverMkcLog = new PjtRecoverMkcLog();
        String comment = "售后退货：" + shouhouId + ",mkc_id:" + shouhouId + ",退货原因：拍机堂退款;故障描述：" + req.getProblem();
        recoverMkcLog.setComment(comment);
        recoverMkcLog.setMkc_id(newMkcId);
        recoverMkcLog.setInuser("系统");
        recoverMkcLog.setShowType(0);

        oaAsyncRabbitTemplate.convertAndSend(
                "oaAsync", JSON.toJSONString(
                        MqLogVO.builder().act(RECOVER_MKC_LOG_ACT).data(recoverMkcLog).build()));

        result.setShouhouId(shouhouId);
        result.setNewMkcId(newMkcId);


        Shouhou shouhouNew = new Shouhou();
        BeanUtils.copyProperties(shouhou, shouhouNew);
        shouhouNew.setId(null);
        shouhouNew.setFromshouhouid(shouhouId);
        shouhouNew.setMkcId(newMkcId);
        shouhouNew.setUserid(76783L);
        this.save(shouhouNew);

        MkcDellogs mkcDellogs = new MkcDellogs();
        mkcDellogs.setMkcId(newMkcId);
        mkcDellogs.setInuser("系统");
        mkcDellogs.setDtime(LocalDateTime.now());
        mkcDellogs.setAreaid(recoverMarketinfo.getAreaid());
        mkcDellogs.setComment(req.getProblem());
        mkcDellogs.setKinds("h3");
        mkcDellogs.setCheck1(true);
        mkcDellogs.setCheck1dtime(LocalDateTime.now());
        mkcDellogs.setCheck1user("系统");
        mkcDellogs.setCheck2(true);
        mkcDellogs.setCheck2dtime(LocalDateTime.now());
        mkcDellogs.setCheck2user("系统");
        BigDecimal price1 = BigDecimal.valueOf(recoverMkc.getInprice())
                .add(BigDecimal.valueOf((Objects.isNull(recoverMkc.getAddprice()) ? 0 : recoverMkc.getAddprice())));
        mkcDellogs.setPrice1(price1);
        mkcDellogs.setPrice2(BigDecimal.ZERO);
        mkcDellogs.setPpriceid(ppriceid);
        mkcDellogs.setFrareaid(String.valueOf(recoverMarketinfo.getAreaid()));
        mkcDellogs.setYouhuiPrice(0D);
        mkcDellogs.setLpToAreaId(recoverMarketinfo.getAreaid());
        mkcDellogsService.save(mkcDellogs);

        return result;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void qujiUseServiceRecord(Integer id, Shouhou shouhou) {
        if(ObjectUtil.defaultIfNull(shouhou.getServiceType(),0) <=0){
            //没有出险直接返回
            return;
        }
        //找到新的枚举值
        List<Integer> serviceTypes = Arrays.stream(ServiceEnum.values()).filter(se -> se.getShouHouType() != null)
                .filter(se -> Objects.equals(shouhou.getServiceType(), se.getShouHouType().getCode()))
                //通过新的枚举值来查找,兼容旧的枚举值
                .flatMap(se -> Stream.concat(Stream.of(se.getCode()), Arrays.stream(JiujiServiceTypeEnum.values())
                        .filter(jjst -> Objects.equals(se.getCode(), jjst.getNewCode())).map(JiujiServiceTypeEnum::getCode)))
                .distinct()
                .collect(Collectors.toList());

        if(serviceTypes.isEmpty()){
            throw new CustomizeException(StrUtil.format("[{}]没有对应的服务枚举值",shouhou.getServiceType()));
        }
        // 是否高级出险
        List<ShouhouLogBo> shouhouLogs = ObjectUtil.defaultIfNull(getShouhouLogs(shouhou.getId()), Collections.emptyList());
        shouhouLogs = shouhouLogs.stream().sorted(Comparator.comparing(ShouhouLogBo::getDTimeSecond, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        Optional<ShouhouLogBo> outServiceLogOpt = shouhouLogs.stream()
                .filter(sLog -> StrUtil.contains(sLog.getComment(), GAOJI_RANK_OUT_SERVICE) || StrUtil.contains(sLog.getComment(), ORDINARY_OUT_SERVICE))
                .findFirst();
        boolean isGaoJiOutService = outServiceLogOpt.filter(sLog -> StrUtil.contains(sLog.getComment(), GAOJI_RANK_OUT_SERVICE)).isPresent();
        // 出险的时候修改为出险的串号
        Set<String> outServiceImeis = CollUtil.newHashSet();
        String outServiceImei = outServiceLogOpt.map(sLog -> StrUtil.subBetween(sLog.getComment(), OUT_SERVICE_IMIE_STRAT, OUT_SERVICE_IMIE_END))
                .filter(StrUtil::isNotBlank).orElse(shouhou.getImei());
        outServiceImeis.add(outServiceImei);
        ShouhouImeichange imeichange = SpringUtil.getBean(ShouhouImeichangeService.class).getTransferOutImeiChange(outServiceImei);
        if (imeichange != null) {
            outServiceImeis.add(imeichange.getImei1());
        }
        //增加售后换机的串号
        shouhouLogs.stream().filter(sLog -> StrUtil.contains(sLog.getComment(), "imei更换为:") && StrUtil.contains(sLog.getComment(), "售后换机"))
                .map(sLog -> StrUtil.subBetween(sLog.getComment(), "imei更换为:", "售后换机"))
                .map(StrUtil::trim).filter(StrUtil::isNotBlank).findFirst().ifPresent(outServiceImeis::add);
        AtomicReference<UseServiceRecordBo> serviceRecordRef = new AtomicReference<>();
        BooleanSupplier outServiceFun = () -> {
            UseServiceRecordBo serviceRecord = serviceRecordService.getTop1UseServiceRecord(shouhou, serviceTypes, isGaoJiOutService, outServiceImeis);
            if (serviceRecord == null) {
                //找不到记录
                return false;
            }
            serviceRecordRef.set(serviceRecord);
            //已经出过险,返回 true
            if(ObjectUtil.defaultIfNull(serviceRecord.getServerShouhouId(),0)>0
                    || ObjectUtil.defaultIfNull(serviceRecord.getFeiYong(),BigDecimal.ZERO).compareTo(BigDecimal.ZERO)>0){
                return true;
            }
            log.debug("补偿出险记录id: {}", serviceRecord.getId());

            //服务出险
            return serviceRecordService.useServiceRecord(shouhou, serviceRecord.getId());
        };
        List<Boolean> outServiceResultList = new LinkedList<>();
        //实时库出险
        outServiceResultList.add(outServiceFun.getAsBoolean());
        //历史库出险
        if(XtenantEnum.isJiujiXtenant() && (serviceRecordRef.get() == null || (serviceRecordRef.get() != null
                && MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS,() -> serviceRecordService.lambdaQuery()
                .eq(ServiceRecord::getId, serviceRecordRef.get().getId()).count()) > 0))
        ){
            // 新机订单历史库需要同步出险
            boolean histOutResult = MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS_WRITE,outServiceFun::getAsBoolean);
            outServiceResultList.add(histOutResult);
        }
        boolean outServiceResult = outServiceResultList.stream().anyMatch(osr -> Boolean.TRUE.equals(osr));
        if(!Boolean.TRUE.equals(outServiceResult) && !isGaoJiOutService){
            throw new CustomizeException(StrUtil.format("维修单[{}]服务出险失败!", id));
        }
    }

    @Override
    public void sendCouponsParts(Shouhou shouhou) {
        //输出不开放这个功能
        if(XtenantEnum.isSaasXtenant()){
            return;
        }
        List<Wxkcoutput> wxkcoutputs = wxkcoutputService.lambdaQuery()
                .eq(Wxkcoutput::getWxid, shouhou.getId())
                .ne(Wxkcoutput::getStats, Wxkcoutput.StatusEnum.CANCELED.getCode())
                .list();
        if(CollectionUtils.isEmpty(wxkcoutputs)){
            return;
        }
        SubCheckChangedParam subCheckChangedParam = new SubCheckChangedParam();
        //维修配件信息封装
        List<SubCheckChangedParam.RepairInfo> repairInfoList = wxkcoutputs.stream().map(wxkcoutput -> {
            SubCheckChangedParam.RepairInfo repairInfo = new SubCheckChangedParam.RepairInfo();
            repairInfo.setPpid(wxkcoutput.getPpriceid());
            repairInfo.setRepairId(wxkcoutput.getId());
            return repairInfo;
        }).collect(Collectors.toList());
        subCheckChangedParam.setRepairInfos(repairInfoList);
        //cityid封装
        Optional.ofNullable(shouhou.getAreaid()).ifPresent(areaId -> {
            Areainfo area = Optional.ofNullable(areainfoService.getByIdSqlServer(areaId)).orElse(new Areainfo());
            subCheckChangedParam.setCityId(area.getCityid());
        });
        subCheckChangedParam.setProductType(NumberConstant.FOUR);
        subCheckChangedParam.setUserId(Convert.toInt(shouhou.getUserid()));
        subCheckChangedParam.setSub_id(shouhou.getId());
        subCheckChangedParam.setExtendType(NumberConstant.TWO);
        Result<List<RepairBuyCouponVO>> listResult = webCloud.sendRepairBuyCoupon(subCheckChangedParam);
        log.warn("调用主站接口发送优惠券传入参数：{}，返回结果：{}",JSONUtil.toJsonStr(subCheckChangedParam), JSONUtil.toJsonStr(listResult));
        if(listResult.isSuccess()){
            List<RepairBuyCouponVO> data = listResult.getData();
            if(CollectionUtils.isEmpty(data)){
                return;
            }
            Map<Integer, Productinfo> productMap = productinfoService.getProductMapByPpids(data.stream().map(RepairBuyCouponVO::getPpid).collect(Collectors.toList()));
            data.forEach(item->{
                Productinfo productinfo = productMap.getOrDefault(item.getPpid(), new Productinfo());
                StringBuilder comment = new StringBuilder();
                String productName = Optional.ofNullable(productinfo.getProductName()).orElse("") + Optional.ofNullable(productinfo.getProductColor()).orElse("");
                comment.append(productName).append("，买即赠券发送优惠券成功，优惠券：");
                //优惠码拼接
                StringJoiner joiner = new StringJoiner(",");
                List<RepairBuyCouponVO.NumberCard> cardIds = item.getCardIds();
                if(CollectionUtils.isNotEmpty(cardIds)){
                    cardIds.forEach(card->{
                        String title = String.format("标题：%s，限制金额：%s，限制1：%s", card.getTitle(), card.getLimitPrice(), card.getLimit1());
                        joiner.add( String.format(" <a class='yhmTip' data= %s title= %s > %s </a>", card.getCardId(), title, card.getCardId()));
                    });
                }
                comment.append(joiner);
                ShouhouLogNoticeBo notice = new ShouhouLogNoticeBo();
                notice.setNeedNotice(false);
                shouhouLogsService.addShouhouLog("系统", shouhou.getId(), ShouHouLogTypeEnum.CLXX.getCode(),
                        comment.toString(),notice, false, null);
            });
        } else {
            throw new CustomizeException("调用主站接口发送优惠券失败："+Optional.ofNullable(listResult.getUserMsg()).orElse(listResult.getMsg()));
        }
    }

    @Override
    public Boolean addCutScreenReqLogShouhou(CutScreenShouhouReq req) {
        if (!XtenantEnum.isJiujiXtenant()) {
            return true;
        }
        Integer shouhouId = req.getShouhouId();
        String inuser = req.getInuser();
        String comment = req.getComment();
        ShouhouLogNoticeBo shouhouLogNoticeBo = new ShouhouLogNoticeBo();
        shouhouLogNoticeBo.setNeedNotice(false);
        shouhouLogsService.addShouhouLog(inuser, shouhouId, ShouHouLogTypeEnum.CLXX.getCode(),
                comment, shouhouLogNoticeBo, false, 0);
        return true;
    }

    @Override
    public DouYinCouponLogRes getLastDouYinCouponLogByShouYingId(Integer shouyingId) {
        return baseMapper.getLastDouYinCouponLogByShouYingId(shouyingId);
    }

    @Override
    public DouYinCouponLogRes getLastDouYinCouponLog(Integer shouhouId, Integer subKinds) {
        return baseMapper.getLastDouYinCouponLog(shouhouId, subKinds);
    }


    private ServiceRecord buildServiceRecord(Shouhou shouhou, ProductServiceOpeningVO pso, Integer wxkcId, double price) {
        //转换为服务记录
        LocalDateTime today = shouhou.getOfftime();
        LocalDateTime startTime = today.toLocalDate().atStartOfDay().plus(pso.getDelayTime(), ChronoUnit.DAYS);
        LocalDateTime endTime = startTime.toLocalDate().atStartOfDay().plus(pso.getDuration(), ChronoUnit.MONTHS)
                .minus(NumberConstant.ONE, ChronoUnit.SECONDS);

        return new ServiceRecord().setBasketId(wxkcId).setSubId(shouhou.getId()).setBasketIdbind(wxkcId).setTradedate(today)
                .setImei(shouhou.getImei()).setServiceType(pso.getType()).setUserid(Convert.toInt(shouhou.getUserid()))
                .setIsdel(Boolean.FALSE).setPrice(price).setPpriceid(pso.getPpid())
                .setFeiyong(0D).setAreaid(ObjectUtil.defaultIfNull(shouhou.getToareaid(), shouhou.getAreaid()))
                .setStartTime(startTime).setEndTime(endTime);
    }

    private void checkAndAddCost(WxFeeBo wxFeeBo, boolean autoOutPut, Boolean isKcLock, Map<Integer, Boolean> distinctMap
            , List<R<ShouhouCostPriceRes>> rList, BindPpidInfoBo infoBo) {
        if(infoBo.getOutPutNumber() == null){
            infoBo.setOutPutNumber(0);
        }

        distinctMap.put(infoBo.getPpid(),Boolean.TRUE);
        Optional<ProductSimpleKcRes> bfOpt = Optional.ofNullable(productinfoService.searchProductKC(null, infoBo.getPpid()))
                .filter(CollectionUtils::isNotEmpty)
                .map(kcList->kcList.get(0));
        if(bfOpt.isPresent()){
            ProductSimpleKcRes kc = bfOpt.get();
            if(infoBo.getId()>0){
                //小于零,为兼容旧代码选项,不需要进行绑定规则校验
                //判断是否允许负库存出库 || 是否上限和库存之内(两者取最小值)
                ShouhouPpidBind bind = shouhouPpidBindService.getById(infoBo.getId());
                if(!Boolean.TRUE.equals(bind.getNegative())
                        && CompareUtil.compare(Math.min(kc.getLcount(),Optional.ofNullable(bind.getOutPutLimit()).orElse(0))
                        , infoBo.getOutPutNumber(),false)<0){
                    rList.add(R.error(String.format("[%s]出库数量必须小于规则上限和库存", infoBo.getPpid())));
                    return;
                }
            }
            WxFeeBo bf = kc.toWxFeeBo(wxFeeBo);
            for (Integer i = 0; i < infoBo.getOutPutNumber(); i++) {
                //库存操作日志: 维修自动出库,售后单：shouhouid,主配件: wxkId / 维修绑定出库,售后单: shouhouid,主配件: wxkId
                bf.setOperateProductKcLog(String.format("%s,售后单id：%s,主配件ppid: %s,配置id: %s"
                        ,DecideUtil.iif(autoOutPut , "维修自动出库" , "维修绑定出库"), wxFeeBo.getShouhouId(), wxFeeBo.getPpid(), infoBo.getId()));
                R<ShouhouCostPriceRes> scpR = this.addCostPrice(bf, false, isKcLock);
                scpR.setUserMsg(String.format("[%s]%s", bf.getPpid(),scpR.getUserMsg()));
                rList.add(scpR);
            }
        }
    }

}
