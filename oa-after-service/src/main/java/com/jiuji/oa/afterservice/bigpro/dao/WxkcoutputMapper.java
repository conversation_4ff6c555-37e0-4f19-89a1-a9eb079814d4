package com.jiuji.oa.afterservice.bigpro.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.bigpro.bo.HexiaoBo;
import com.jiuji.oa.afterservice.bigpro.bo.WxPeiJianBo;
import com.jiuji.oa.afterservice.bigpro.bo.wxpj.PjTransferBo;
import com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput;
import com.jiuji.oa.afterservice.bigpro.vo.res.WeiXiuPjRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-31
 */
@Mapper
public interface WxkcoutputMapper extends BaseMapper<Wxkcoutput> {
    List<HexiaoBo> getHexiao(@Param("wxId") Integer wxId,@Param("xTenant") Integer xTenant);

    /**
     * 查询维修配件信息
     * @param shouhouId
     * @return
     */
    List<WeiXiuPjRes> getWeixiuPJ(@Param("shouhouId") Integer shouhouId);

    /**
     * 查询出库库存id
     * @param wxId
     * @return
     */
    Integer getKcIdByWxId(@Param("wxId") Integer wxId);

    /**
     * 查询维修库存条数
     * @param shouhouId
     * @return
     */
    Integer getWxKcCount(@Param("shouhouId") Integer shouhouId);

    /**
     * 优惠码使用校验
     * @param shouhouId
     * @param limitIdList
     * @return
     */
    List<Integer> cidCheck(@Param("shouhouId") Integer shouhouId, @Param("limitIdList") List<Integer> limitIdList);

    /**
     * 优惠码使用校验
     * @param shouhouId
     * @param limitIdList
     * @return
     */
    List<Integer> cidCheck1(@Param("shouhouId") Integer shouhouId, @Param("limitIdList") List<Integer> limitIdList);


    /**
     * 根据售后id查询维修配件信息
     * @param shouhouId
     * @return
     */
    List<WxPeiJianBo> getWxPeijianByShouhouId(@Param("shouhouId") Integer shouhouId);

    /**
     * 获取配件订购申请信息
     *
     * @param transferId
     * @param orderType
     * @return
     */
    List<PjTransferBo> getTransferSubInfo(@Param("transferId") Integer transferId, @Param("orderType") Integer orderType);

    /**
     * 获取维修单
     * @param id
     * @param xtenant
     * @param areaId
     * @param hasAuthPart
     * @param authorizeId
     * @return
     */
    Wxkcoutput getByIdAndXtenantAndAuthPart(@Param("id") Integer id, @Param("xtenant") Integer xtenant, @Param("areaId") Integer areaId
            , @Param("hasAuthPart") boolean hasAuthPart, @Param("authorizeId") Integer authorizeId);

    /**
     * 获取可以分摊优惠码费用的配件
     * @param shouhouId
     * @param wxkcIds
     * @param shouhouServicesPpriceids 服务类不参与分摊
     * @return
     */
    List<Wxkcoutput> listYouhuiMaAllocation(@Param("shouhouId") Integer shouhouId, @Param("wxkcIds") Set<Integer> wxkcIds, @Param("shouhouServicesPpriceids") List<Integer> shouhouServicesPpriceids);

    /**
     * 批量更新分摊的优惠费用
     * @param shouhouId
     * @param wxkcoutputs
     * @return
     */
    int updateYouHuiFeiYongBatch(@Param("shouhouId") Integer shouhouId, @Param("youHuiMa") String youHuiMa, @Param("wxkcoutputs") List<Wxkcoutput> wxkcoutputs);

    /**
     * 获取撤销配件分摊的费用
     * @param wxId
     * @return
     */
    BigDecimal sumCancelPjYouHuiFeiyong(@Param("wxId") Integer wxId);

    int updateServiceType(@Param("servicePjIds") List<Integer> servicePjIds, @Param("serviceType") Integer serviceType);

    /**
     * 取消指定的服务配件
     * @param wxId
     * @param serviceType
     * @return
     */
    int cancelServiceType(@Param("wxId") Integer wxId, @Param("serviceType") Integer serviceType);
}
