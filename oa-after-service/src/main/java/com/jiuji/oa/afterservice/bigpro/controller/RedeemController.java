package com.jiuji.oa.afterservice.bigpro.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.jiuji.oa.afterservice.bigpro.bo.RedeemContext;
import com.jiuji.oa.afterservice.bigpro.po.MemberPointExchangeCouponModel;
import com.jiuji.oa.afterservice.bigpro.po.PointExchangeCouponOutput;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.service.RedeemService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouExService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.vo.PointExchangeMaxCouponReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.rabbitmq.SmallproRabblitMq;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.annotation.LogRecordAround;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * 维修配件重构
 */
@Slf4j
@RestController
@RequestMapping("/api/Redeem")
public class RedeemController {

    @Resource
    private RedeemService redeemService;
    @Resource
    private ShouhouExService shouhouExService;
    @Resource
    private ShouhouService shouhouService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;




    /**
     * 第一：如果过为空那就是全部门店使用，
     * 第二：如果存在具体门店id那就这几个门店id使用
     * 第三：如果只存在-1 那就是所有门店不使用
     * @return
     */
    @GetMapping("/isUseRedeem")
    public R<Boolean> isUseRedeem(){
        return R.success(redeemService.isUseRedeem());
    }


    /**
     * 查询积分兑换券
     * @param req
     * @return
     */
    @PostMapping("/pointExchangeMaxCoupon")
    public R<PointExchangeMaxCouponRes> pointExchangeMaxCoupon(@RequestBody @Validated PointExchangeMaxCouponReq req){
        return R.success(redeemService.pointExchangeMaxCoupon(req));
    }


    /**
     * 提供给主站查询是否可以用积分兑换券
     * @param req
     * @return
     */
    @LogRecordAround(value = "主站查询规则码是否可以使用")
    @PostMapping("/canUseCoupon")
    public R<List<CanUseCouponOutputRes>> canUseCoupon(@RequestBody @Validated CanUseCouponOutputReq req){
        List<CanUseCouponOutputRes> canUseCouponOutputRes = redeemService.CanUseCoupon(req);
        return R.success(canUseCouponOutputRes);
    }



    /**
     * 前端优惠码查询
     * @param req
     * @return
     */
    @PostMapping("/getMemberPointExchangeCouponByUserId")
    public R<MemberPointExchangeRes> getMemberPointExchangeCouponByUserId(@RequestBody @Validated MemberPointExchangeCouponReq req){
        MemberPointExchangeRes memberPointExchangeCouponByUserId = redeemService.getMemberPointExchangeCouponByUserId(req);
        return R.success(memberPointExchangeCouponByUserId);
    }


    /**
     * 主站查询维修单可以用优惠码
     * @param req
     * @return
     */
    @LogRecordAround(value = "主站查询维修单可以用优惠码")
    @PostMapping("/getYouHuiMaBySub")
    public R<List<YouHuiMaBySubRes>> getYouHuiMaBySub(@RequestBody @Validated YouHuiMaBySubReq req){
        List<YouHuiMaBySubRes> youHuiMaBySub = redeemService.getYouHuiMaBySub(req);
        return R.success(youHuiMaBySub);
    }



    /**
     * 最优推荐
     * @param req
     * @return
     */
    @GetMapping("/getPreferentialRecommendation")
    public R<List<GetPreferentialRecommendationOutputRes>> getPreferentialRecommendation(@RequestParam("shouHouId") Integer shouHouId){
        RedeemContext.setFilterReason(new HashMap<>());
        List<GetPreferentialRecommendationOutputRes> preferentialRecommendation = redeemService.getPreferentialRecommendation(shouHouId);
        R<List<GetPreferentialRecommendationOutputRes>> success = R.success(preferentialRecommendation);
        //封装过滤原因
        Map<String, Object> filterReason = RedeemContext.getFilterReason();
        RedeemContext.clearFilterReason();
        if(MapUtil.isNotEmpty(filterReason)){
            success.setExData(filterReason);
            log.warn("维修单：{}，过滤原因：{}", shouHouId,JSONUtil.toJsonStr(filterReason));
        }
        return success;
    }


    /**
     * 一键使用
     * @param req
     * @return
     */
    @PostMapping("/oneClickUse")
    public R<Boolean> oneClickUse(@RequestBody @Validated OneClickUseReq req){
        redeemService.oneClickUse(req);
        return R.success("成功");
    }

    /**
     * 积分兑换券
     * @param req
     * @return
     */
    @PostMapping("/pointExchangeCoupon")
    public R<PointExchangeCouponOutput> pointExchangeCoupon(@RequestBody @Validated PointExchangeCouponOutputReq req){
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("请登录"));
        //对换成为优惠券之后
        PointExchangeCouponOutput pointExchangeCouponOutput = redeemService.pointExchangeCoupon(req);
        //自动使用优惠券
        shouhouExService.useYouhuiMa(req.getShouHouId(), pointExchangeCouponOutput.getCouponMa(), userBO.getUserName());
        return R.success(pointExchangeCouponOutput);
    }


    /**
     * 撤销优惠券
     * @param req
     * @return
     */
    @PostMapping("/cancelExchangeCoupon")
    public R cancelExchangeCoupon(@RequestBody @Validated CancelExchangeCouponOutputReq req){
        return redeemService.cancelExchangeCoupon(req);
    }


    @LogRecordAround(value = "主站使用维修单优惠码")
    @PostMapping("/youhuimaUse")
    public R<Boolean> youhuimaUse(@RequestBody YouhuimaUseReq req) {
        Shouhou shouhou = shouhouService.getById(req.getShouHouId());
        // 模拟登录 系统用户信息
        redeemService.simulateLogin(shouhou.getAreaid());
        return shouhouExService.useYouhuiMa(req.getShouHouId(), req.getCode(), req.getOperateUser());
    }



}
